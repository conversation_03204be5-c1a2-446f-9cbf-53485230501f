# SurrealDB实施计划

## 1. 项目概述

### 1.1 目标
使用SurrealDB完全替代Elasticsearch，实现统一数据库架构，提供更好的性能和开发体验。

### 1.2 核心优势
- **架构简化**: 单一数据库系统，减少运维复杂度
- **实时能力**: Live Query提供原生实时数据推送
- **性能提升**: 消除数据同步开销，减少网络延迟
- **功能增强**: 图数据库关系、ACID事务、向量搜索

## 2. 实施阶段

### 阶段1: 基础设施搭建 (2-3小时)

#### Task 1: 项目结构和依赖配置
- 更新Cargo.toml，添加SurrealDB依赖
- 移除Elasticsearch相关依赖
- 配置SurrealDB连接参数

```toml
[dependencies]
surrealdb = { version = "1.0", features = ["kv-rocksdb", "scripting"] }
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
```

#### Task 2-4: 基础组件
- 错误处理系统适配SurrealDB
- 配置管理更新数据库配置
- 日志系统集成SurrealDB操作日志

### 阶段2: SurrealDB数据层实现 (4-5小时)

#### Task 5: 数据模型定义
```rust
// 适配SurrealDB的数据模型
#[derive(Debug, Serialize, Deserialize)]
pub struct DataSource {
    pub id: Option<Thing>,
    pub name: String,
    pub description: Option<String>,
    pub connector_config: ConnectorConfig,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

#### Task 6: SurrealDB客户端封装
```rust
pub struct SurrealDBClient {
    db: Arc<Surreal<Any>>,
}

impl SurrealDBClient {
    pub async fn new(config: &DatabaseConfig) -> Result<Self, Error> {
        let db = Surreal::new::<Ws>(&config.url).await?;
        // 认证和数据库选择
        Ok(Self { db: Arc::new(db) })
    }
}
```

### 阶段3: 搜索和实时功能 (3-4小时)

#### Task 7: SurrealDB全文搜索配置
```sql
-- 定义搜索分析器
DEFINE ANALYZER search_analyzer 
    TOKENIZERS blank, punct, camel, class
    FILTERS lowercase, ascii, ngram(2,3);

-- 定义搜索索引
DEFINE INDEX datasource_search ON TABLE datasource 
    FIELDS name, description 
    SEARCH ANALYZER search_analyzer BM25 HIGHLIGHTS;
```

#### Task 8: 实时查询和缓存实现
```rust
pub async fn setup_live_query(&self) -> Result<(), Error> {
    let mut stream = self.db
        .query("LIVE SELECT * FROM datasource")
        .await?
        .stream::<DataSource>(0)?;
        
    // 处理实时数据变更
    while let Some(result) = stream.next().await {
        // 更新缓存或通知客户端
    }
}
```

#### Task 9: DataSource仓库层实现
```rust
impl DataSourceRepository {
    pub async fn search(&self, query: &SearchQuery) -> Result<SearchResponse, Error> {
        let sql = "
            SELECT *, search::score(1) AS score, 
                   search::highlight('<b>', '</b>', 1) AS highlight
            FROM datasource 
            WHERE name @1@ $query OR description @1@ $query
            ORDER BY score DESC
            LIMIT $limit
        ";
        
        let results = self.db
            .query(sql)
            .bind(("query", &query.q))
            .bind(("limit", query.size.unwrap_or(10)))
            .await?;
            
        Ok(self.format_search_response(results))
    }
}
```

### 阶段4: 业务逻辑层 (2-3小时)

#### Task 10-12: 业务服务集成
- 验证服务适配SurrealDB数据类型
- 集成搜索、实时查询和缓存功能
- 实现统一的业务服务接口

### 阶段5: API层实现 (3-4小时)

#### Task 13-16: API处理器和路由
- 认证中间件保持不变
- 更新请求处理器以使用SurrealDB
- 添加实时查询API端点
- 配置路由和服务器启动

### 阶段6: 测试和优化 (3-4小时)

#### Task 17-19: 测试和性能优化
- SurrealDB功能单元测试
- 搜索功能集成测试
- 性能基准测试和优化

## 3. 关键实现细节

### 3.1 拼音搜索实现

```rust
// 自定义拼音搜索实现
pub struct PinyinSearchService {
    db: Arc<Surreal<Any>>,
    pinyin_map: HashMap<char, Vec<String>>,
}

impl PinyinSearchService {
    pub async fn search_with_pinyin(&self, query: &str) -> Result<Vec<DataSource>, Error> {
        // 将查询转换为拼音
        let pinyin_query = self.convert_to_pinyin(query);
        
        // 执行拼音搜索
        let sql = "
            SELECT *, search::score(2) AS score
            FROM datasource 
            WHERE name @2@ $pinyin_query OR description @2@ $pinyin_query
            ORDER BY score DESC
        ";
        
        let results = self.db
            .query(sql)
            .bind(("pinyin_query", pinyin_query))
            .await?;
            
        Ok(results.take(0)?)
    }
}
```

### 3.2 实时数据推送

```rust
// WebSocket实时推送
pub struct RealtimeService {
    db: Arc<Surreal<Any>>,
    clients: Arc<RwLock<HashMap<String, WebSocket>>>,
}

impl RealtimeService {
    pub async fn start_live_query(&self) -> Result<(), Error> {
        let mut stream = self.db
            .query("LIVE SELECT * FROM datasource")
            .await?
            .stream::<DataSource>(0)?;

        while let Some(notification) = stream.next().await {
            match notification {
                Ok(data) => {
                    // 推送给所有连接的客户端
                    self.broadcast_to_clients(data).await?;
                }
                Err(e) => {
                    log::error!("Live Query错误: {}", e);
                }
            }
        }
        
        Ok(())
    }
}
```

### 3.3 性能优化策略

```rust
// 查询优化
impl QueryOptimizer {
    pub fn optimize_search_query(&self, query: &str) -> String {
        // 1. 查询预处理
        let processed_query = self.preprocess_query(query);
        
        // 2. 索引提示
        let optimized_sql = format!(
            "SELECT *, search::score(1) AS score
             FROM datasource WITH INDEX datasource_search
             WHERE name @1@ $query OR description @1@ $query
             ORDER BY score DESC"
        );
        
        optimized_sql
    }
    
    pub async fn setup_query_cache(&self) -> Result<(), Error> {
        // 设置查询结果缓存
        self.db.query("
            DEFINE PARAM $cache_ttl VALUE 300s;
            DEFINE FUNCTION fn::cached_search($query: string) {
                RETURN SELECT * FROM datasource 
                WHERE name @@ $query 
                ORDER BY search::score(1) DESC
                CACHE $cache_ttl;
            };
        ").await?;
        
        Ok(())
    }
}
```

## 4. 迁移检查清单

### 4.1 功能对等性验证
- [ ] 基础CRUD操作
- [ ] 全文搜索功能
- [ ] 模糊搜索功能
- [ ] 拼音搜索功能
- [ ] 搜索结果高亮
- [ ] 分页和排序
- [ ] 聚合查询

### 4.2 性能验证
- [ ] 搜索响应时间 < 100ms
- [ ] 并发查询支持 > 1000 QPS
- [ ] 内存使用合理
- [ ] 实时查询延迟 < 10ms

### 4.3 稳定性验证
- [ ] 长时间运行稳定
- [ ] 错误恢复机制
- [ ] 数据一致性保证
- [ ] 事务处理正确

## 5. 风险控制

### 5.1 技术风险
- **拼音搜索**: 需要自定义实现，可能影响搜索准确性
- **复杂聚合**: SurrealDB聚合功能可能不如ES灵活
- **生态系统**: SurrealDB生态相对较新

### 5.2 缓解措施
- 实现完整的功能对比测试
- 保留ES代码作为备选方案
- 分阶段迁移，确保每个阶段稳定

### 5.3 回滚计划
- 保持原有ES代码分支
- 数据双写验证期
- 快速切换机制

## 6. 成功指标

### 6.1 功能指标
- API兼容性 100%
- 搜索准确性 > 95%
- 实时查询延迟 < 10ms

### 6.2 性能指标
- 响应时间提升 > 30%
- 内存使用减少 > 20%
- 运维复杂度降低 > 50%

### 6.3 开发效率
- 代码行数减少 > 15%
- 测试覆盖率 > 90%
- 部署复杂度降低

---

这个实施计划提供了详细的迁移路径，确保从Elasticsearch到SurrealDB的平滑过渡，同时充分利用SurrealDB的优势特性。
