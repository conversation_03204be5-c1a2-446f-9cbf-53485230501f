# 内置模型提供商配置文件
# 此文件定义了系统初始化时需要导入的内置模型提供商
# 系统启动时会自动导入这些配置到数据库中

[metadata]
version = "1.0.0"
description = "内置模型提供商配置"
last_updated = "2025-01-15T10:30:00Z"

# DeepSeek AI
[[providers]]
id = "deepseek"
name = "深度求索"
api_key = ""
api_type = "openai"
base_url = "https://api.deepseek.com/v1"
icon = "font_deepseek"
enabled = false
builtin = true
description = "提供高效灵活的大模型API服务，支持复杂场景任务，具备高性价比优势。"

[[providers.models]]
name = "deepseek-chat"

[[providers.models]]
name = "deepseek-reasoner"

# OpenAI
[[providers]]
id = "openai"
name = "OpenAI"
api_key = ""
api_type = "openai"
base_url = "https://api.openai.com"
icon = "/assets/icons/llm/openai.svg"
enabled = false
builtin = true
description = "提供先进的GPT系列大模型（如GPT-4/ChatGPT），支持多模态交互与企业级AI解决方案，具备成熟的API生态与顶尖的通用智能表现。"

[[providers.models]]
name = "gpt-4o-mini"

[[providers.models]]
name = "gpt-4o"

# Ollama
[[providers]]
id = "ollama"
name = "Ollama"
api_key = ""
api_type = "ollama"
base_url = "http://127.0.0.1:11434"
icon = "/assets/icons/llm/ollama.svg"
enabled = false
builtin = true
description = "一键部署主流开源模型，实现私有化 AI 推理与微调，保障数据隐私与本地化控制。"

[[providers.models]]
name = "qwen2.5:32b"

[[providers.models]]
name = "deepseek-r1:32b"

[[providers.models]]
name = "deepseek-r1:14b"

[[providers.models]]
name = "deepseek-r1:8b"

# Gitee AI (模力方舟)
[[providers]]
id = "gitee_ai"
name = "模力方舟"
api_key = ""
api_type = "openai"
base_url = "https://ai.gitee.com"
icon = "font_gitee"
enabled = false
builtin = true
description = "模力方舟（Gitee AI），汇聚了最新最热的 AI 模型，提供模型体验、推理、微调、部署和应用的一站式服务。"

[[providers.models]]
name = "deepseek-ai/DeepSeek-R1"

[[providers.models]]
name = "deepseek-ai/DeepSeek-V3"

[[providers.models]]
name = "deepseek-ai/DeepSeek-R1-Distill-Qwen-7B"

# 通义千问
[[providers]]
id = "qianwen"
name = "通义千问"
api_key = ""
api_type = "openai"
base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
icon = "font_tongyiqianwenTongyi-Qianwen"
enabled = false
builtin = true
description = "阿里云自研的通义大模型，支持全模态模型服务调用，强推理高效率低成本，满足更多业务场景。"

[[providers.models]]
name = "tongyi-intent-detect-v3"

[[providers.models]]
name = "deepseek-r1-distill-qwen-32b"

[[providers.models]]
name = "deepseek-r1"

[[providers.models]]
name = "qwen-max"

[[providers.models]]
name = "qwq-plus"

[[providers.models]]
name = "qwen2.5-32b-instruct"

# OpenAI Compatible
[[providers]]
id = "openai_compatible"
name = "OpenAI-API-compatible"
api_key = ""
api_type = "openai"
base_url = ""
icon = "font_openai"
enabled = false
builtin = true
description = "全兼容 OpenAI API 接口的替代方案，提供更低成本/更高并发的模型调用，支持私有化部署与多模型托管。"

[[providers.models]]
name = "deepseek-r1"

# Coco AI (自定义配置)
[[providers]]
id = "coco"
name = "Coco AI"
api_key = ""  # 将在运行时从环境变量或配置中读取
api_type = "openai"  # 默认值，可在运行时覆盖
base_url = ""  # 将在运行时从配置中读取
icon = "font_coco"
enabled = false  # 默认禁用，需要用户配置后启用
builtin = true
description = "Coco AI 自定义模型提供商，用于配置默认 AI 助手"

[[providers.models]]
name = "default-model"  # 占位符，将在运行时替换

# 硅基流动
[[providers]]
id = "silicon_flow"
name = "硅基流动"
api_key = ""
api_type = "openai"
base_url = "https://api.siliconflow.cn"
icon = "font_siliconflow"
enabled = false
builtin = true
description = "硅基流动提供对各种模型（LLM、文本嵌入、重排序、STT、TTS）的访问，可通过模型名称、API密钥和其他参数进行配置"

[[providers.models]]
name = "BAAI/bge-m3"

[[providers.models]]
name = "deepseek-ai/DeepSeek-R1"

[[providers.models]]
name = "deepseek-ai/DeepSeek-V3"

# 腾讯混元
[[providers]]
id = "tencent_hunyuan"
name = "腾讯混元"
api_key = ""
api_type = "openai"
base_url = "https://api.hunyuan.cloud.tencent.com"
icon = "font_hunyuan"
enabled = false
builtin = true
description = "腾讯混元提供的模型，例如 hunyuan-standard、 hunyuan-standard-256k, hunyuan-pro, hunyuan-role…"

[[providers.models]]
name = "hunyuan-pro"

[[providers.models]]
name = "hunyuan-standard"

[[providers.models]]
name = "hunyuan-lite"

[[providers.models]]
name = "hunyuan-standard-256k"

[[providers.models]]
name = "hunyuan-vision"

[[providers.models]]
name = "hunyuan-code"

[[providers.models]]
name = "hunyuan-role"

[[providers.models]]
name = "hunyuan-turbo"

# Google Gemini
[[providers]]
id = "gemini"
name = "Gemini"
api_key = ""
api_type = "openai"
base_url = "https://generativelanguage.googleapis.com"
icon = "font_gemini-ai"
enabled = false
builtin = true
description = "谷歌提供的 Gemini 模型"

[[providers.models]]
name = "gemini-2.0-flash"

[[providers.models]]
name = "gemini-1.5-flash"

[[providers.models]]
name = "gemini-1.5-pro"

# 月之暗面
[[providers]]
id = "moonshot"
name = "月之暗面"
api_key = ""
api_type = "openai"
base_url = "https://api.moonshot.cn"
icon = "font_Moonshot"
enabled = false
builtin = true
description = "Moonshot 提供的模型，例如 moonshot-v1-8k、moonshot-v1-32k 和 moonshot-v1-128k。"

[[providers.models]]
name = "moonshot-v1-auto"

# Minimax
[[providers]]
id = "minimax"
name = "Minimax"
api_key = ""
api_type = "openai"
base_url = "https://api.minimax.chat/v1/"
icon = "font_MiniMax"
enabled = false
builtin = true
description = "MiniMax 是一个先进的AI平台，提供一系列为各种应用设计的强大模型，包括LLMs。"

[[providers.models]]
name = "abab5.5s"

[[providers.models]]
name = "abab6.5s"

[[providers.models]]
name = "abab6.5g"

[[providers.models]]
name = "abab6.5t"

[[providers.models]]
name = "minimax-01"

# 火山方舟
[[providers]]
id = "volcanoArk"
name = "火山方舟"
api_key = ""
api_type = "openai"
base_url = "https://ark.cn-beijing.volces.com/api/v3/"
icon = "font_VolcanoArk"
enabled = false
builtin = true
description = "火山方舟提供的模型，例如 Doubao-pro-4k、Doubao-pro-32k 和 Doubao-pro-128k。"

[[providers.models]]
name = "doubao-1.5-vision-pro"

[[providers.models]]
name = "doubao-1.5-pro-32k"

[[providers.models]]
name = "doubao-1.5-pro-32k-character"

[[providers.models]]
name = "Doubao-1.5-pro-256k"

# 百度云千帆
[[providers]]
id = "qianfan"
name = "百度云千帆"
api_key = ""
api_type = "openai"
base_url = "https://qianfan.baidubce.com/v2/"
icon = "font_Qianfan"
enabled = false
builtin = true
description = "预置全系列文心大模型与上百个精选第三方模型"

[[providers.models]]
name = "ERNIE-4.0"

[[providers.models]]
name = "ERNIE 4.0 Trubo"

[[providers.models]]
name = "ERNlE Speed"

[[providers.models]]
name = "ERNIE Lite"

[[providers.models]]
name = "BGE Large ZH"

[[providers.models]]
name = "BGE Large EN"
