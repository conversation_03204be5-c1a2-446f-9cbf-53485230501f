# 认证配置文件
auth:
  # JWT配置
  jwt:
    # JWT密钥（生产环境应使用环境变量 JWT_SECRET）
    secret: "coco-server-jwt-secret-key-change-in-production"
    # JWT过期时间（秒）
    expiry: 86400  # 24小时
    # JWT签发者
    issuer: "coco-server"
    # JWT受众
    audience: "coco-app"

  # API令牌配置
  api_tokens:
    # API令牌过期时间（秒）
    expiry: 31536000  # 365天
    # 令牌长度
    length: 64

  # 密码配置
  password:
    # bcrypt cost（4-31，推荐12）
    bcrypt_cost: 12
    # 默认密码文件路径
    password_file: ".password"
    # 用户配置文件路径
    profile_file: ".user_profile"

  # 开发环境配置
  development:
    # 是否启用开发模式令牌
    enable_dev_tokens: true
    # 开发模式令牌列表
    dev_tokens:
      - "test-token-1"
      - "test-token-2"
      - "abc123"

  # 会话配置
  session:
    # 会话超时时间（秒）
    timeout: 3600  # 1小时
    # 是否启用记住我功能
    remember_me: true
    # 记住我过期时间（秒）
    remember_me_expiry: 2592000  # 30天

  # 安全配置
  security:
    # 是否启用速率限制
    rate_limiting: true
    # 登录尝试限制（每分钟）
    login_attempts_per_minute: 5
    # 是否启用CSRF保护
    csrf_protection: false
    # 是否要求HTTPS
    require_https: false

# 默认用户配置
default_user:
  # 用户ID
  id: "coco-default-user"
  # 用户名
  username: "coco-default-user"
  # 邮箱
  email: ""
  # 角色列表
  roles:
    - "admin"
  # 默认语言
  language: "zh-CN"
  # 默认主题
  theme: "light"
