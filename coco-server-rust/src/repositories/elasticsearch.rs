use crate::error::error::CocoError;
use elasticsearch::{
    auth::Credentials,
    http::transport::Transport,
    indices::{IndicesCreateParts, IndicesExistsParts},
    Elasticsearch, Error as EsError,
};
use serde_json::{json, Value};
use std::sync::Arc;
use tracing::{error, info, warn};

/// Elasticsearch客户端封装
///
/// 提供统一的Elasticsearch操作接口，包括连接管理、索引操作等
#[derive(Clone)]
pub struct ElasticsearchClient {
    /// Elasticsearch客户端实例
    client: Arc<Elasticsearch>,

    /// 默认索引名称
    default_index: String,
}

impl ElasticsearchClient {
    /// 创建新的Elasticsearch客户端
    ///
    /// # 参数
    /// * `url` - Elasticsearch服务器URL
    /// * `username` - 用户名（可选）
    /// * `password` - 密码（可选）
    /// * `default_index` - 默认索引名称
    pub async fn new(
        url: &str,
        username: Option<&str>,
        password: Option<&str>,
        default_index: String,
    ) -> Result<Self, CocoError> {
        info!("正在连接到Elasticsearch: {}", url);

        // 构建传输层
        let transport = if let (Some(user), Some(pass)) = (username, password) {
            // 使用认证
            let credentials = Credentials::Basic(user.to_string(), pass.to_string());
            Transport::cloud(url, credentials).map_err(|e| {
                CocoError::ServerError(format!("创建Elasticsearch传输层失败: {}", e))
            })?
        } else {
            // 无认证
            Transport::single_node(url).map_err(|e| {
                CocoError::ServerError(format!("创建Elasticsearch传输层失败: {}", e))
            })?
        };

        let client = Arc::new(Elasticsearch::new(transport));

        let es_client = Self {
            client,
            default_index,
        };

        // 测试连接
        es_client.test_connection().await?;

        // 确保索引存在
        es_client.ensure_index_exists().await?;

        info!("Elasticsearch客户端初始化成功");
        Ok(es_client)
    }

    /// 测试Elasticsearch连接
    async fn test_connection(&self) -> Result<(), CocoError> {
        match self.client.ping().send().await {
            Ok(response) => {
                if response.status_code().is_success() {
                    info!("Elasticsearch连接测试成功");
                    Ok(())
                } else {
                    error!(
                        "Elasticsearch连接测试失败: 状态码 {}",
                        response.status_code()
                    );
                    Err(CocoError::ServerError(
                        "Elasticsearch连接测试失败".to_string(),
                    ))
                }
            }
            Err(e) => {
                error!("Elasticsearch连接测试失败: {}", e);
                Err(CocoError::ServerError(format!(
                    "Elasticsearch连接失败: {}",
                    e
                )))
            }
        }
    }

    /// 确保索引存在，如果不存在则创建
    async fn ensure_index_exists(&self) -> Result<(), CocoError> {
        // 检查索引是否存在
        let exists_response = self
            .client
            .indices()
            .exists(IndicesExistsParts::Index(&[&self.default_index]))
            .send()
            .await
            .map_err(|e| CocoError::ServerError(format!("检查索引是否存在失败: {}", e)))?;

        if exists_response.status_code().is_success() {
            info!("索引 '{}' 已存在", self.default_index);
            return Ok(());
        }

        // 创建索引
        info!("正在创建索引: {}", self.default_index);

        let mapping = self.get_datasource_mapping();
        let create_response = self
            .client
            .indices()
            .create(IndicesCreateParts::Index(&self.default_index))
            .body(mapping)
            .send()
            .await
            .map_err(|e| CocoError::ServerError(format!("创建索引失败: {}", e)))?;

        if create_response.status_code().is_success() {
            info!("索引 '{}' 创建成功", self.default_index);
            Ok(())
        } else {
            let error_text = create_response
                .text()
                .await
                .unwrap_or_else(|_| "未知错误".to_string());
            error!("创建索引失败: {}", error_text);
            Err(CocoError::ServerError(format!(
                "创建索引失败: {}",
                error_text
            )))
        }
    }

    /// 获取DataSource索引映射配置
    fn get_datasource_mapping(&self) -> Value {
        json!({
            "mappings": {
                "properties": {
                    "id": {
                        "type": "keyword",
                        "copy_to": "combined_fulltext"
                    },
                    "type": {
                        "type": "keyword",
                        "copy_to": "combined_fulltext"
                    },
                    "name": {
                        "type": "keyword",
                        "copy_to": "combined_fulltext"
                    },
                    "description": {
                        "type": "text",
                        "copy_to": "combined_fulltext"
                    },
                    "icon": {
                        "enabled": false
                    },
                    "category": {
                        "type": "keyword"
                    },
                    "tags": {
                        "type": "keyword"
                    },
                    "connector": {
                        "type": "object",
                        "properties": {
                            "id": {
                                "type": "keyword"
                            },
                            "config": {
                                "enabled": false
                            }
                        }
                    },
                    "sync_enabled": {
                        "type": "keyword"
                    },
                    "enabled": {
                        "type": "keyword"
                    },
                    "combined_fulltext": {
                        "type": "text"
                    },
                    "created": {
                        "type": "date"
                    },
                    "updated": {
                        "type": "date"
                    }
                }
            }
        })
    }

    /// 获取Elasticsearch客户端实例
    pub fn client(&self) -> &Elasticsearch {
        &self.client
    }

    /// 获取默认索引名称
    pub fn default_index(&self) -> &str {
        &self.default_index
    }
}

/// Elasticsearch错误转换
impl From<EsError> for CocoError {
    fn from(error: EsError) -> Self {
        CocoError::ServerError(format!("Elasticsearch错误: {}", error))
    }
}
