use crate::config::config_manager::ConfigManager;
use crate::services::token_service::TokenService;
use std::sync::Arc;

/// 应用状态结构
#[derive(Clone)]
pub struct AppState {
    pub config_manager: Arc<ConfigManager>,
    pub token_service: Arc<TokenService>,
}

impl AppState {
    pub fn new(config_manager: Arc<ConfigManager>, token_service: Arc<TokenService>) -> Self {
        Self {
            config_manager,
            token_service,
        }
    }
}
