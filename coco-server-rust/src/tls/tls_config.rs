use anyhow::Result;
use rustls_pemfile::{certs, pkcs8_private_keys};
use std::fs::File;
use std::io::BufReader;
use tokio_rustls::rustls::{Certificate, PrivateKey, ServerConfig};
use tracing::info;

pub struct TlsConfig {
    pub cert_path: String,
    pub key_path: String,
}

impl TlsConfig {
    pub fn new(cert_path: String, key_path: String) -> Self {
        TlsConfig {
            cert_path,
            key_path,
        }
    }

    pub fn load_config(&self) -> Result<ServerConfig> {
        // 读取证书文件
        let cert_file = File::open(&self.cert_path)
            .map_err(|e| anyhow::anyhow!("Failed to open certificate file '{}': {}", self.cert_path, e))?;
        let mut cert_reader = BufReader::new(cert_file);
        let certs = certs(&mut cert_reader)
            .map_err(|e| anyhow::anyhow!("Failed to parse certificate file: {}", e))?
            .into_iter()
            .map(Certificate)
            .collect::<Vec<_>>();

        info!("Loaded {} certificates from {}", certs.len(), self.cert_path);

        // 读取私钥文件
        let key_file = File::open(&self.key_path)
            .map_err(|e| anyhow::anyhow!("Failed to open private key file '{}': {}", self.key_path, e))?;
        let mut key_reader = BufReader::new(key_file);
        let mut keys = pkcs8_private_keys(&mut key_reader)
            .map_err(|e| anyhow::anyhow!("Failed to parse private key file: {}", e))?;

        if keys.is_empty() {
            return Err(anyhow::anyhow!("No private keys found in {}", self.key_path));
        }

        let key = PrivateKey(keys.remove(0));

        info!("Loaded private key from {}", self.key_path);

        // 创建TLS配置
        let config = ServerConfig::builder()
            .with_safe_defaults()
            .with_no_client_auth()
            .with_single_cert(certs, key)
            .map_err(|e| anyhow::anyhow!("Failed to create TLS server config: {}", e))?;

        Ok(config)
    }

    pub fn is_enabled(&self) -> bool {
        // 检查证书和私钥文件是否存在
        std::fs::metadata(&self.cert_path).is_ok() && std::fs::metadata(&self.key_path).is_ok()
    }
}