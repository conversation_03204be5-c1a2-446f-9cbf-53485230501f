use axum::{
    http::StatusCode,
    response::{IntoResponse, Response},
    Json,
};
use serde_json::json;
use thiserror::Error;

#[derive(Error, Debug)]
pub enum CocoError {
    #[error("Configuration error: {0}")]
    ConfigError(String),

    #[error("IO error: {0}")]
    IoError(#[from] std::io::Error),

    #[error("YAML parsing error: {0}")]
    YamlError(#[from] serde_yaml::Error),

    #[error("JSON parsing error: {0}")]
    JsonError(#[from] serde_json::Error),

    #[error("Authentication error: {0}")]
    AuthError(String),

    #[error("WebSocket error: {0}")]
    WebSocketError(String),

    #[error("Server error: {0}")]
    ServerError(String),

    #[error("Invalid request: {0}")]
    InvalidRequest(String),

    #[error("Not found: {0}")]
    NotFound(String),

    #[error("Password hashing error: {0}")]
    BcryptError(#[from] bcrypt::BcryptError),

    #[error("Database error: {0}")]
    Database(String),

    // ModelProvider相关错误
    #[error("Model provider not found: {0}")]
    ModelProviderNotFound(String),

    #[error("Model provider validation error: {0}")]
    ModelProviderValidation(String),

    #[error("Built-in model provider cannot be modified: {0}")]
    BuiltinProviderProtection(String),

    #[error("Model provider operation forbidden: {0}")]
    ModelProviderForbidden(String),
}

impl CocoError {
    pub fn config(msg: &str) -> Self {
        CocoError::ConfigError(msg.to_string())
    }

    pub fn auth(msg: &str) -> Self {
        CocoError::AuthError(msg.to_string())
    }

    pub fn websocket(msg: &str) -> Self {
        CocoError::WebSocketError(msg.to_string())
    }

    pub fn server(msg: &str) -> Self {
        CocoError::ServerError(msg.to_string())
    }

    pub fn invalid_request(msg: &str) -> Self {
        CocoError::InvalidRequest(msg.to_string())
    }

    // ModelProvider错误构造函数
    pub fn model_provider_not_found(id: &str) -> Self {
        CocoError::ModelProviderNotFound(format!("Model provider with id '{}' not found", id))
    }

    pub fn model_provider_validation(msg: &str) -> Self {
        CocoError::ModelProviderValidation(msg.to_string())
    }

    pub fn builtin_provider_protection(msg: &str) -> Self {
        CocoError::BuiltinProviderProtection(msg.to_string())
    }

    pub fn model_provider_forbidden(msg: &str) -> Self {
        CocoError::ModelProviderForbidden(msg.to_string())
    }

    pub fn unauthorized(msg: &str) -> Self {
        Self::AuthError(msg.to_string())
    }

    pub fn not_found(msg: &str) -> Self {
        Self::NotFound(msg.to_string())
    }
}

// 实现IntoResponse trait，将CocoError转换为HTTP响应
impl IntoResponse for CocoError {
    fn into_response(self) -> Response {
        let (status, message) = match self {
            // 客户端错误 (4xx)
            CocoError::InvalidRequest(msg) => (StatusCode::BAD_REQUEST, msg),
            CocoError::AuthError(msg) => (StatusCode::UNAUTHORIZED, msg),
            CocoError::NotFound(msg) => (StatusCode::NOT_FOUND, msg),
            CocoError::ModelProviderNotFound(msg) => (StatusCode::NOT_FOUND, msg),
            CocoError::ModelProviderValidation(msg) => (StatusCode::BAD_REQUEST, msg),
            CocoError::BuiltinProviderProtection(msg) => (StatusCode::FORBIDDEN, msg),
            CocoError::ModelProviderForbidden(msg) => (StatusCode::FORBIDDEN, msg),

            // 服务器错误 (5xx)
            CocoError::ConfigError(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            CocoError::ServerError(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            CocoError::Database(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),
            CocoError::WebSocketError(msg) => (StatusCode::INTERNAL_SERVER_ERROR, msg),

            // 其他错误映射
            CocoError::IoError(err) => (StatusCode::INTERNAL_SERVER_ERROR, err.to_string()),
            CocoError::YamlError(err) => (StatusCode::BAD_REQUEST, err.to_string()),
            CocoError::JsonError(err) => (StatusCode::BAD_REQUEST, err.to_string()),
            CocoError::BcryptError(err) => (StatusCode::INTERNAL_SERVER_ERROR, err.to_string()),
        };

        let body = Json(json!({
            "error": message
        }));

        (status, body).into_response()
    }
}
