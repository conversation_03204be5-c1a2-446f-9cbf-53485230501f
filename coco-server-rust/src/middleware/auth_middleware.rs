use crate::auth::user_claims::UserClaims;
use crate::services::token_service::TokenService;
use axum::{
    extract::State,
    http::{Request, StatusCode},
    middleware::Next,
    response::{IntoResponse, Response},
    Json,
};
use jsonwebtoken::{decode, DecodingKey, Validation};
use serde_json::json;
use std::sync::Arc;
use tracing::info;

// 身份验证中间件
pub async fn auth_middleware<B>(
    request: Request<B>,
    next: Next<B>,
) -> Result<Response, impl IntoResponse>
where
    B: Send,
{
    info!("Processing authentication for request to {}", request.uri());

    // 检查是否需要跳过身份验证的路径
    let path = request.uri().path().to_string();
    if should_skip_auth(&path) {
        info!("Skipping authentication for path: {}", path);
        let response = next.run(request).await;
        info!("Response from handler for path: {}", path);
        return Ok(response);
    }

    // 从请求头中获取API令牌
    // 支持两种格式：X-API-TOKEN 和 Authorization: Bearer
    let token = request
        .headers()
        .get("X-API-TOKEN")
        .and_then(|value| value.to_str().ok())
        .or_else(|| {
            // 检查Authorization头中的Bearer令牌
            request
                .headers()
                .get("Authorization")
                .and_then(|value| value.to_str().ok())
                .and_then(|auth_header| {
                    if auth_header.starts_with("Bearer ") {
                        Some(&auth_header[7..]) // 移除"Bearer "前缀
                    } else {
                        None
                    }
                })
        });

    // 验证令牌（在实际应用中应该从配置或数据库中获取有效令牌列表）
    match token {
        Some(token) => match is_valid_token(token, None).await {
            Ok(true) => {
                info!("Authentication successful for token: {}", mask_token(token));
                Ok(next.run(request).await)
            }
            Ok(false) => {
                info!("Authentication failed: invalid token provided");
                let error_response = json!({
                    "error": "Invalid authentication token",
                    "message": "提供的API令牌无效。请检查令牌是否正确。"
                });
                Err((StatusCode::UNAUTHORIZED, Json(error_response)))
            }
            Err(error_msg) => {
                info!(
                    "Authentication failed: invalid token provided - {}",
                    error_msg
                );
                let error_response = json!({
                    "error": "Invalid authentication token",
                    "message": format!("提供的API令牌无效: {}。请检查令牌是否正确。", error_msg)
                });
                Err((StatusCode::UNAUTHORIZED, Json(error_response)))
            }
        },
        None => {
            info!("Authentication failed: missing token");
            let error_response = json!({
                "error": "Missing authentication token",
                "message": "缺少API令牌。请在请求头中提供X-API-TOKEN。"
            });
            Err((StatusCode::UNAUTHORIZED, Json(error_response)))
        }
    }
}

// 检查是否应该跳过身份验证的路径
fn should_skip_auth(path: &str) -> bool {
    // /setup/_initialize, /health 和 /account/login 端点不需要身份验证
    path == "/setup/_initialize" || path == "/health" || path == "/account/login"
}

// 验证令牌是否有效（支持JWT令牌和API令牌）
async fn is_valid_token(
    token: &str,
    token_service: Option<&TokenService>,
) -> Result<bool, &'static str> {
    // 首先尝试验证JWT令牌
    let secret = get_jwt_secret();
    match decode::<UserClaims>(
        token,
        &DecodingKey::from_secret(secret.as_ref()),
        &Validation::default(),
    ) {
        Ok(_) => return Ok(true),
        Err(_) => {
            // 如果JWT验证失败，检查是否为API令牌
            if let Some(service) = token_service {
                match service.validate_api_token(token).await {
                    Ok(_) => {
                        // 更新令牌最后使用时间
                        let _ = service.update_last_used(token).await;
                        return Ok(true);
                    }
                    Err(_) => {
                        // API令牌验证失败，继续检查开发模式令牌
                    }
                }
            }

            // 开发模式下的测试令牌
            if is_development_mode() {
                const DEV_TOKENS: &[&str] = &["test-token-1", "test-token-2", "abc123"];
                if DEV_TOKENS.contains(&token) {
                    return Ok(true);
                }
            }

            // 检查令牌格式
            if token.is_empty() {
                Err("令牌为空")
            } else if token.len() < 10 {
                Err("令牌格式无效")
            } else {
                Err("令牌无效或已过期")
            }
        }
    }
}

/// 获取JWT密钥，优先从环境变量获取，否则使用默认值
fn get_jwt_secret() -> String {
    std::env::var("JWT_SECRET")
        .unwrap_or_else(|_| "coco-server-jwt-secret-key-change-in-production".to_string())
}

/// 检查是否为开发模式
fn is_development_mode() -> bool {
    std::env::var("RUST_ENV").unwrap_or_else(|_| "development".to_string()) == "development"
}

// 遮蔽令牌以用于日志记录（只显示前几个字符和后几个字符）
fn mask_token(token: &str) -> String {
    if token.len() <= 4 {
        "*".repeat(token.len())
    } else {
        let prefix = &token[..2];
        let suffix = &token[token.len() - 2..];
        format!("{}***{}", prefix, suffix)
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use axum::{
        body::Body,
        http::{Request, StatusCode},
        middleware::from_fn,
        routing::get,
        Router,
    };
    use tower::ServiceExt;

    #[tokio::test]
    async fn test_auth_middleware_allows_setup_initialize() {
        let app = Router::new()
            .route("/setup/_initialize", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder()
            .uri("/setup/_initialize")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_auth_middleware_blocks_without_token() {
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder().uri("/test").body(Body::empty()).unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }

    #[tokio::test]
    async fn test_auth_middleware_allows_with_valid_token() {
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder()
            .uri("/test")
            .header("X-API-TOKEN", "test-token-1")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_auth_middleware_allows_with_bearer_token() {
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder()
            .uri("/test")
            .header("Authorization", "Bearer test-token-1")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::OK);
    }

    #[tokio::test]
    async fn test_auth_middleware_blocks_with_invalid_token() {
        let app = Router::new()
            .route("/test", get(|| async { "ok" }))
            .route_layer(from_fn(auth_middleware));

        let request = Request::builder()
            .uri("/test")
            .header("X-API-TOKEN", "invalid-token")
            .body(Body::empty())
            .unwrap();

        let response = app.oneshot(request).await.unwrap();
        assert_eq!(response.status(), StatusCode::UNAUTHORIZED);
    }
}
