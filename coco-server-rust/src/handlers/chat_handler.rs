use axum::{
    extract::State,
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::{info, error};

use crate::config::config_manager::ConfigManager;

// 聊天请求模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatRequest {
    pub model: String,
    pub messages: Vec<ChatMessage>,
    pub temperature: Option<f64>,
    pub max_tokens: Option<u32>,
    pub stream: Option<bool>,
    pub stop: Option<Vec<String>>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessage {
    pub role: String,
    pub content: String,
    pub name: Option<String>,
}

// 聊天响应模型
#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatResponse {
    pub id: String,
    pub object: String,
    pub created: i64,
    pub model: String,
    pub choices: Vec<ChatChoice>,
    pub usage: Option<Usage>,
}

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ChatChoice {
    pub index: i32,
    pub message: ChatMessage,
    pub finish_reason: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Usage {
    pub prompt_tokens: Option<u32>,
    pub completion_tokens: Option<u32>,
    pub total_tokens: Option<u32>,
}

// 错误响应模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatErrorResponse {
    pub error: ChatError,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatError {
    pub message: String,
    pub r#type: String,
    pub param: Option<String>,
    pub code: Option<String>,
}

pub async fn chat_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(payload): Json<ChatRequest>,
) -> impl IntoResponse {
    info!("Handling POST /api/v1/chat request for model: {}", payload.model);
    
    // 验证请求参数
    if let Err(error_response) = validate_chat_request(&payload) {
        return (
            StatusCode::BAD_REQUEST,
            Json(error_response),
        ).into_response();
    }
    
    // 检查模型是否可用
    if !is_model_available(&payload.model).await {
        let error_response = ChatErrorResponse {
            error: ChatError {
                message: format!("模型 '{}' 不可用", payload.model),
                r#type: "model_not_found".to_string(),
                param: Some("model".to_string()),
                code: Some("model_not_found".to_string()),
            },
        };
        return (
            StatusCode::NOT_FOUND,
            Json(error_response),
        ).into_response();
    }
    
    // 处理聊天请求
    match process_chat_request(&payload, config_manager).await {
        Ok(response) => {
            info!("Chat request processed successfully");
            (StatusCode::OK, Json(response)).into_response()
        }
        Err(error_msg) => {
            error!("Chat request processing failed: {}", error_msg);
            let error_response = ChatErrorResponse {
                error: ChatError {
                    message: format!("聊天处理失败: {}", error_msg),
                    r#type: "api_error".to_string(),
                    param: None,
                    code: None,
                },
            };
            (
                StatusCode::INTERNAL_SERVER_ERROR,
                Json(error_response),
            ).into_response()
        }
    }
}

fn validate_chat_request(request: &ChatRequest) -> Result<(), ChatErrorResponse> {
    // 验证消息不为空
    if request.messages.is_empty() {
        return Err(ChatErrorResponse {
            error: ChatError {
                message: "消息列表不能为空".to_string(),
                r#type: "invalid_request_error".to_string(),
                param: Some("messages".to_string()),
                code: None,
            },
        });
    }
    
    // 验证消息格式
    for (i, message) in request.messages.iter().enumerate() {
        if message.role.is_empty() {
            return Err(ChatErrorResponse {
                error: ChatError {
                    message: format!("消息 {} 的角色不能为空", i + 1),
                    r#type: "invalid_request_error".to_string(),
                    param: Some("messages".to_string()),
                    code: None,
                },
            });
        }
        
        if message.content.is_empty() {
            return Err(ChatErrorResponse {
                error: ChatError {
                    message: format!("消息 {} 的内容不能为空", i + 1),
                    r#type: "invalid_request_error".to_string(),
                    param: Some("messages".to_string()),
                    code: None,
                },
            });
        }
        
        // 验证角色值
        if !["system", "user", "assistant", "function"].contains(&message.role.as_str()) {
            return Err(ChatErrorResponse {
                error: ChatError {
                    message: format!("无效的角色 '{}'", message.role),
                    r#type: "invalid_request_error".to_string(),
                    param: Some("messages".to_string()),
                    code: None,
                },
            });
        }
    }
    
    // 验证temperature范围
    if let Some(temp) = request.temperature {
        if temp < 0.0 || temp > 2.0 {
            return Err(ChatErrorResponse {
                error: ChatError {
                    message: "temperature 必须在 0.0 和 2.0 之间".to_string(),
                    r#type: "invalid_request_error".to_string(),
                    param: Some("temperature".to_string()),
                    code: None,
                },
            });
        }
    }
    
    // 验证max_tokens
    if let Some(max_tokens) = request.max_tokens {
        if max_tokens == 0 || max_tokens > 32000 {
            return Err(ChatErrorResponse {
                error: ChatError {
                    message: "max_tokens 必须在 1 和 32000 之间".to_string(),
                    r#type: "invalid_request_error".to_string(),
                    param: Some("max_tokens".to_string()),
                    code: None,
                },
            });
        }
    }
    
    Ok(())
}

async fn is_model_available(model_id: &str) -> bool {
    // 从.llm_config文件中获取配置的模型
    use std::fs;
    use serde_json::Value;
    
    if let Ok(config_content) = fs::read_to_string(".llm_config") {
        if let Ok(config_json) = serde_json::from_str::<Value>(&config_content) {
            if let Some(config_model) = config_json.get("model").and_then(|m| m.as_str()) {
                if config_model == model_id {
                    return true;
                }
            }
        }
    }
    
    // 检查默认模型列表
    let default_models = get_default_models();
    default_models.iter().any(|m| m == model_id)
}

fn get_default_models() -> Vec<String> {
    vec![
        "gpt-3.5-turbo".to_string(),
        "gpt-4".to_string(),
        "gpt-4-turbo".to_string(),
        "claude-3-sonnet-20240229".to_string(),
        "claude-3-opus-20240229".to_string(),
        "claude-3-haiku-20240307".to_string(),
    ]
}

async fn process_chat_request(
    request: &ChatRequest,
    _config_manager: Arc<ConfigManager>,
) -> Result<ChatResponse, String> {
    // 这是一个模拟的聊天处理实现
    // 在实际应用中，这里会调用相应的LLM API（OpenAI、Claude等）
    
    info!("Processing chat request with {} messages", request.messages.len());
    
    // 模拟处理时间
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;
    
    // 获取最后一条用户消息
    let last_user_message = request.messages
        .iter()
        .rev()
        .find(|msg| msg.role == "user")
        .map(|msg| msg.content.clone())
        .unwrap_or_else(|| "未知消息".to_string());
    
    // 生成模拟响应
    let assistant_response = generate_mock_response(&last_user_message, &request.model);
    
    // 创建聊天响应
    let response = ChatResponse {
        id: format!("chatcmpl-{}", chrono::Utc::now().timestamp()),
        object: "chat.completion".to_string(),
        created: chrono::Utc::now().timestamp(),
        model: request.model.clone(),
        choices: vec![ChatChoice {
            index: 0,
            message: ChatMessage {
                role: "assistant".to_string(),
                content: assistant_response.clone(),
                name: None,
            },
            finish_reason: Some("stop".to_string()),
        }],
        usage: Some(Usage {
            prompt_tokens: Some(calculate_tokens(&request.messages)),
            completion_tokens: Some(calculate_response_tokens(&assistant_response)),
            total_tokens: Some(calculate_tokens(&request.messages) + calculate_response_tokens(&assistant_response)),
        }),
    };
    
    Ok(response)
}

fn generate_mock_response(user_message: &str, model: &str) -> String {
    // 根据模型和用户消息生成模拟响应
    if model.contains("claude") {
        format!("我是Claude助手，这是对您消息'{}'的模拟响应。", user_message)
    } else if model.contains("gpt-4") {
        format!("我是GPT-4，正在处理您的消息：'{}'。这是模拟响应。", user_message)
    } else {
        format!("收到您的消息：'{}'。这是来自{}的模拟回复。", user_message, model)
    }
}

fn calculate_tokens(messages: &[ChatMessage]) -> u32 {
    // 简单的token计算模拟
    let total_chars: usize = messages.iter()
        .map(|msg| msg.content.len())
        .sum();
    (total_chars / 4) as u32 // 假设平均每个token对应4个字符
}

fn calculate_response_tokens(response: &str) -> u32 {
    // 简单的响应token计算模拟
    (response.len() / 4) as u32
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_chat_request_validation() {
        let valid_request = ChatRequest {
            model: "gpt-3.5-turbo".to_string(),
            messages: vec![ChatMessage {
                role: "user".to_string(),
                content: "Hello".to_string(),
                name: None,
            }],
            temperature: Some(0.7),
            max_tokens: Some(100),
            stream: None,
            stop: None,
        };
        
        assert!(validate_chat_request(&valid_request).is_ok());
    }

    #[test]
    fn test_empty_messages_validation() {
        let invalid_request = ChatRequest {
            model: "gpt-3.5-turbo".to_string(),
            messages: vec![],
            temperature: None,
            max_tokens: None,
            stream: None,
            stop: None,
        };
        
        assert!(validate_chat_request(&invalid_request).is_err());
    }

    #[test]
    fn test_invalid_role_validation() {
        let invalid_request = ChatRequest {
            model: "gpt-3.5-turbo".to_string(),
            messages: vec![ChatMessage {
                role: "invalid_role".to_string(),
                content: "Hello".to_string(),
                name: None,
            }],
            temperature: None,
            max_tokens: None,
            stream: None,
            stop: None,
        };
        
        assert!(validate_chat_request(&invalid_request).is_err());
    }

    #[test]
    fn test_temperature_validation() {
        let valid_request = ChatRequest {
            model: "gpt-3.5-turbo".to_string(),
            messages: vec![ChatMessage {
                role: "user".to_string(),
                content: "Hello".to_string(),
                name: None,
            }],
            temperature: Some(1.5),
            max_tokens: None,
            stream: None,
            stop: None,
        };
        
        assert!(validate_chat_request(&valid_request).is_ok());
        
        let invalid_request = ChatRequest {
            model: "gpt-3.5-turbo".to_string(),
            messages: vec![ChatMessage {
                role: "user".to_string(),
                content: "Hello".to_string(),
                name: None,
            }],
            temperature: Some(3.0),
            max_tokens: None,
            stream: None,
            stop: None,
        };
        
        assert!(validate_chat_request(&invalid_request).is_err());
    }

    #[test]
    fn test_chat_response_structure() {
        let response = ChatResponse {
            id: "test-id".to_string(),
            object: "chat.completion".to_string(),
            created: 1234567890,
            model: "gpt-3.5-turbo".to_string(),
            choices: vec![ChatChoice {
                index: 0,
                message: ChatMessage {
                    role: "assistant".to_string(),
                    content: "Test response".to_string(),
                    name: None,
                },
                finish_reason: Some("stop".to_string()),
            }],
            usage: Some(Usage {
                prompt_tokens: Some(10),
                completion_tokens: Some(5),
                total_tokens: Some(15),
            }),
        };
        
        assert_eq!(response.id, "test-id");
        assert_eq!(response.object, "chat.completion");
        assert_eq!(response.choices.len(), 1);
        assert_eq!(response.choices[0].message.role, "assistant");
    }
}