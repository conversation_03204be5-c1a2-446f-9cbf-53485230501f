use crate::config::config_manager::Config<PERSON>anager;
use crate::health::health_checker::<PERSON><PERSON>he<PERSON>;
use axum::{extract::State, http::StatusCode, response::IntoResponse, Json};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::info;

// 健康检查响应模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerInfo {
    pub name: String,
    pub endpoint: String,
    pub provider: ProviderInfo,
    pub version: VersionInfo,
    #[serde(rename = "minimal_client_version")]
    pub minimal_client_version: VersionInfo,
    pub updated: String,
    pub public: bool,
    #[serde(rename = "auth_provider")]
    pub auth_provider: AuthProviderInfo,
    pub managed: bool,
    #[serde(rename = "encode_icon_to_base64")]
    pub encode_icon_to_base64: bool,
    #[serde(rename = "setup_required")]
    pub setup_required: bool,
    pub health: HealthInfo,
    pub stats: StatsInfo,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ProviderInfo {
    pub name: String,
    pub icon: String,
    pub website: String,
    pub eula: String,
    #[serde(rename = "privacy_policy")]
    pub privacy_policy: String,
    pub banner: String,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct VersionInfo {
    pub number: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthProviderInfo {
    pub sso: SsoInfo,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SsoInfo {
    pub url: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct HealthInfo {
    pub status: String,
    pub services: std::collections::HashMap<String, String>,
}

// ServiceInfo 不再需要，因为services现在使用HashMap格式

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct StatsInfo {
    #[serde(rename = "assistant_count")]
    pub assistant_count: i32,
}

pub async fn info_handler(
    State(config_manager): State<Arc<ConfigManager>>,
) -> Result<impl IntoResponse, (StatusCode, String)> {
    info!("Handling /_info request");

    // 构建响应数据
    let server_info = build_server_info(&config_manager).await;

    Ok((StatusCode::OK, Json(server_info)))
}

pub async fn health_handler(
    State(config_manager): State<Arc<ConfigManager>>,
) -> Result<impl IntoResponse, (StatusCode, String)> {
    info!("Handling /health request");

    // 创建健康检查器并检查健康状态
    let health_checker = HealthChecker::new((*config_manager).clone()).map_err(|e| {
        (
            StatusCode::INTERNAL_SERVER_ERROR,
            format!("Failed to create health checker: {}", e),
        )
    })?;

    let health_info = health_checker.check_health().await;

    // 根据整体状态确定HTTP状态码
    let status_code = match health_info.status.as_str() {
        "green" => StatusCode::OK,
        "yellow" => StatusCode::OK, // 黄色状态也应返回200，但会提供详细信息
        "red" => StatusCode::SERVICE_UNAVAILABLE,
        _ => StatusCode::OK,
    };

    Ok((status_code, Json(health_info)))
}

async fn build_server_info(config_manager: &ConfigManager) -> ServerInfo {
    // 获取配置信息
    let config = config_manager.get_config();

    // 从配置中提取信息，使用默认值作为备选
    let server_config = config
        .coco
        .as_ref()
        .and_then(|c| c.server.as_ref())
        .cloned()
        .unwrap_or_default();

    let provider_config = server_config.provider.clone().unwrap_or_default();

    let auth_provider_config = provider_config.auth_provider.clone().unwrap_or_default();

    let sso_config = auth_provider_config.sso.clone().unwrap_or_default();

    // 获取端点信息 - 使用Web服务器端口而不是API服务器端口
    let web_port = config_manager.get_web_port();
    let endpoint = format!("http://localhost:{}", web_port);

    // 创建健康检查器并检查健康状态
    let health_checker =
        HealthChecker::new((*config_manager).clone()).expect("Failed to create health checker");
    let health = health_checker.check_health().await;

    // 构建响应结构
    ServerInfo {
        name: server_config.name.unwrap_or_else(|| "Coco Server".to_string()),
        endpoint: endpoint.clone(),
        provider: ProviderInfo {
            name: provider_config.name.unwrap_or_else(|| "INFINI Labs".to_string()),
            icon: provider_config.icon.unwrap_or_else(|| "https://coco.rs/favicon.ico".to_string()),
            website: provider_config.website.unwrap_or_else(|| "https://coco.rs/".to_string()),
            eula: provider_config.eula.unwrap_or_else(|| "https://coco.rs/#/terms".to_string()),
            privacy_policy: provider_config.privacy_policy.unwrap_or_else(|| "https://coco.rs/privacy".to_string()),
            banner: provider_config.banner.unwrap_or_else(|| "https://coco.rs/svg/connect.svg".to_string()),
            description: provider_config.description.unwrap_or_else(|| "Coco AI Server - Search, Connect, Collaborate, AI-powered enterprise search, all in one space.".to_string()),
        },
        version: VersionInfo {
            number: "1.0.0".to_string(), // 这里应该从配置或环境变量中获取版本号
        },
        minimal_client_version: VersionInfo {
            number: server_config.minimal_client_version
                .and_then(|v| v.number)
                .unwrap_or_else(|| "0.3".to_string()),
        },
        updated: chrono::Utc::now().to_rfc3339(), // 当前时间
        public: server_config.public.unwrap_or(false),
        auth_provider: AuthProviderInfo {
            sso: SsoInfo {
                url: {
                    // 获取配置的SSO URL，如果是相对路径则转换为完整URL
                    let sso_url = sso_config.url.unwrap_or_else(|| {
                        "/sso/login/cloud?provider=coco-cloud&product=coco".to_string()
                    });

                    // 如果URL以"/"开头，说明是相对路径，需要添加完整的endpoint
                    if sso_url.starts_with('/') {
                        format!("{}{}", endpoint, sso_url)
                    } else {
                        // 如果已经是完整URL，直接使用
                        sso_url
                    }
                },
            },
        },
        managed: false, // 这里应该从配置中获取
        encode_icon_to_base64: server_config.encode_icon_to_base64.unwrap_or(false),
        setup_required: false, // 这里应该根据实际状态确定
        health,
        stats: StatsInfo {
            assistant_count: 5, // 这里应该从实际数据中获取
        },
    }
}
