use axum::{
    extract::{Query, State},
    http::{HeaderMap, StatusCode},
    response::J<PERSON>,
};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;
use tracing::info;

use crate::config::config_manager::ConfigManager;
use crate::models::{MCPServer, SearchHit, SearchHits, SearchResponse, SearchTotal};

/// 处理 GET /mcp_server/_search 请求
pub async fn search_mcp_server_get(
    Query(params): Query<HashMap<String, String>>,
    State(_config_manager): State<Arc<ConfigManager>>,
) -> Result<Json<SearchResponse<MCPServer>>, StatusCode> {
    info!("处理GET搜索MCP Server请求: params={:?}", params);

    // 模拟MCP Server搜索结果
    let mock_mcp_servers = vec![
        MCPServer {
            id: Some("mcp_001".to_string()),
            name: "File System MCP".to_string(),
            description: Some("MCP Server for file system operations".to_string()),
            server_type: Some("filesystem".to_string()),
            config: Some(json!({
                "root_path": "/data",
                "read_only": false
            })),
            enabled: true,
            created: Some("2024-01-01T00:00:00Z".to_string()),
            updated: Some("2024-01-01T00:00:00Z".to_string()),
        },
        MCPServer {
            id: Some("mcp_002".to_string()),
            name: "Database MCP".to_string(),
            description: Some("MCP Server for database operations".to_string()),
            server_type: Some("database".to_string()),
            config: Some(json!({
                "host": "localhost",
                "port": 5432,
                "database": "coco"
            })),
            enabled: true,
            created: Some("2024-01-01T00:00:00Z".to_string()),
            updated: Some("2024-01-01T00:00:00Z".to_string()),
        },
    ];

    let hits: Vec<SearchHit<MCPServer>> = mock_mcp_servers
        .into_iter()
        .enumerate()
        .map(|(i, mcp)| SearchHit {
            _index: "mcp_server".to_string(),
            _type: "_doc".to_string(),
            _id: mcp.id.clone().unwrap_or_default(),
            _score: Some(1.0 - (i as f64 * 0.1)),
            _source: mcp,
        })
        .collect();

    let response = SearchResponse {
        took: 3,
        timed_out: false,
        hits: SearchHits {
            total: SearchTotal {
                value: hits.len() as u64,
                relation: "eq".to_string(),
            },
            max_score: Some(1.0),
            hits,
        },
    };

    Ok(Json(response))
}

/// 处理 POST /mcp_server/_search 请求
pub async fn search_mcp_server_post(
    State(_config_manager): State<Arc<ConfigManager>>,
    _headers: HeaderMap,
    Json(query): Json<Value>,
) -> Result<Json<SearchResponse<MCPServer>>, StatusCode> {
    info!("处理POST搜索MCP Server请求: query={:?}", query);

    // 对于 POST 请求，我们可以处理更复杂的查询
    // 这里先返回相同的模拟数据
    let mock_mcp_servers = vec![
        MCPServer {
            id: Some("mcp_001".to_string()),
            name: "File System MCP".to_string(),
            description: Some("MCP Server for file system operations".to_string()),
            server_type: Some("filesystem".to_string()),
            config: Some(json!({
                "root_path": "/data",
                "read_only": false
            })),
            enabled: true,
            created: Some("2024-01-01T00:00:00Z".to_string()),
            updated: Some("2024-01-01T00:00:00Z".to_string()),
        },
        MCPServer {
            id: Some("mcp_002".to_string()),
            name: "Database MCP".to_string(),
            description: Some("MCP Server for database operations".to_string()),
            server_type: Some("database".to_string()),
            config: Some(json!({
                "host": "localhost",
                "port": 5432,
                "database": "coco"
            })),
            enabled: true,
            created: Some("2024-01-01T00:00:00Z".to_string()),
            updated: Some("2024-01-01T00:00:00Z".to_string()),
        },
    ];

    let hits: Vec<SearchHit<MCPServer>> = mock_mcp_servers
        .into_iter()
        .enumerate()
        .map(|(i, mcp)| SearchHit {
            _index: "mcp_server".to_string(),
            _type: "_doc".to_string(),
            _id: mcp.id.clone().unwrap_or_default(),
            _score: Some(1.0 - (i as f64 * 0.1)),
            _source: mcp,
        })
        .collect();

    let response = SearchResponse {
        took: 2,
        timed_out: false,
        hits: SearchHits {
            total: SearchTotal {
                value: hits.len() as u64,
                relation: "eq".to_string(),
            },
            max_score: Some(1.0),
            hits,
        },
    };

    Ok(Json(response))
}

/// 处理 OPTIONS /mcp_server/_search 请求（CORS 预检）
pub async fn search_mcp_server_options() -> Result<StatusCode, StatusCode> {
    info!("处理OPTIONS MCP Server搜索请求");
    Ok(StatusCode::OK)
}
