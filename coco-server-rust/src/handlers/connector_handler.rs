use crate::config::config_manager::ConfigManager;
use crate::models::{Connector, SearchHit, SearchHits, SearchResponse, SearchTotal};
use axum::{
    body::Bytes,
    extract::{Query, State},
    http::{HeaderMap, StatusCode},
    response::Json,
    routing::{get, post},
    Router,
};
use serde::{Deserialize, Serialize};
use serde_json::{json, Value};
use std::collections::HashMap;
use std::sync::Arc;

/// 处理 GET /connector/_search 请求
pub async fn search_connector_get(
    Query(params): Query<HashMap<String, String>>,
    State(config_manager): State<Arc<ConfigManager>>,
) -> Result<Json<SearchResponse<Connector>>, StatusCode> {
    tracing::info!(
        "Handling GET /connector/_search request with params: {:?}",
        params
    );

    // 模拟连接器搜索结果
    let mock_connectors = vec![
        Connector {
            id: "conn_001".to_string(),
            name: "Database Connector".to_string(),
            description: Some("Connect to various databases".to_string()),
            category: Some("database".to_string()),
            icon: Some("database".to_string()),
            config: Some(json!({"type": "postgresql", "ssl": true})),
            created: Some("2024-01-01T00:00:00Z".to_string()),
            updated: Some("2024-01-01T00:00:00Z".to_string()),
        },
        Connector {
            id: "conn_002".to_string(),
            name: "File System Connector".to_string(),
            description: Some("Connect to file systems".to_string()),
            category: Some("filesystem".to_string()),
            icon: Some("folder".to_string()),
            config: Some(json!({"root_path": "/data"})),
            created: Some("2024-01-02T00:00:00Z".to_string()),
            updated: Some("2024-01-02T00:00:00Z".to_string()),
        },
        Connector {
            id: "conn_003".to_string(),
            name: "API Connector".to_string(),
            description: Some("Connect to REST APIs".to_string()),
            category: Some("api".to_string()),
            icon: Some("api".to_string()),
            config: Some(json!({"base_url": "https://api.example.com"})),
            created: Some("2024-01-03T00:00:00Z".to_string()),
            updated: Some("2024-01-03T00:00:00Z".to_string()),
        },
    ];

    let hits: Vec<SearchHit<Connector>> = mock_connectors
        .into_iter()
        .enumerate()
        .map(|(i, conn)| SearchHit {
            _index: "connector".to_string(),
            _type: "_doc".to_string(),
            _id: conn.id.clone(),
            _score: Some(1.0 - (i as f64 * 0.1)),
            _source: conn,
        })
        .collect();

    let response = SearchResponse {
        took: 4,
        timed_out: false,
        hits: SearchHits {
            total: SearchTotal {
                value: hits.len() as u64,
                relation: "eq".to_string(),
            },
            max_score: Some(1.0),
            hits,
        },
    };

    Ok(Json(response))
}

/// 处理 POST /connector/_search 请求
pub async fn search_connector_post(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(query): Json<Value>,
) -> Result<Json<SearchResponse<Connector>>, StatusCode> {
    tracing::info!(
        "Handling POST /connector/_search request with query: {:?}",
        query
    );

    // 对于 POST 请求，我们可以处理更复杂的查询
    // 这里先返回相同的模拟数据
    let mock_connectors = vec![Connector {
        id: "conn_001".to_string(),
        name: "Database Connector".to_string(),
        description: Some("Connect to various databases".to_string()),
        category: Some("database".to_string()),
        icon: Some("database".to_string()),
        config: Some(json!({"type": "postgresql", "ssl": true})),
        created: Some("2024-01-01T00:00:00Z".to_string()),
        updated: Some("2024-01-01T00:00:00Z".to_string()),
    }];

    let hits: Vec<SearchHit<Connector>> = mock_connectors
        .into_iter()
        .enumerate()
        .map(|(i, conn)| SearchHit {
            _index: "connector".to_string(),
            _type: "_doc".to_string(),
            _id: conn.id.clone(),
            _score: Some(1.0 - (i as f64 * 0.1)),
            _source: conn,
        })
        .collect();

    let response = SearchResponse {
        took: 2,
        timed_out: false,
        hits: SearchHits {
            total: SearchTotal {
                value: hits.len() as u64,
                relation: "eq".to_string(),
            },
            max_score: Some(1.0),
            hits,
        },
    };

    Ok(Json(response))
}

/// 处理 OPTIONS /connector/_search 请求（CORS 预检）
pub async fn search_connector_options() -> Result<StatusCode, StatusCode> {
    tracing::info!("Handling OPTIONS /connector/_search request");
    Ok(StatusCode::OK)
}
