use axum::{
    extract::State,
    http::StatusCode,
    response::IntoRespo<PERSON>,
    <PERSON>son,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use tracing::info;

use crate::config::config_manager::Config<PERSON>anager;
use crate::error::result::{AxumResult};

// 模型响应模型
#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct ModelsResponse {
    pub data: Vec<Model>,
    pub object: String,
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct Model {
    pub id: String,
    pub object: String,
    pub created: i64,
    pub owned_by: String,
}

// 默认模型列表
fn get_default_models() -> Vec<Model> {
    vec![
        Model {
            id: "gpt-3.5-turbo".to_string(),
            object: "model".to_string(),
            created: 1677610602,
            owned_by: "openai".to_string(),
        },
        Model {
            id: "gpt-4".to_string(),
            object: "model".to_string(),
            created: 1678604612,
            owned_by: "openai".to_string(),
        },
        Model {
            id: "gpt-4-turbo".to_string(),
            object: "model".to_string(),
            created: 1700630280,
            owned_by: "openai".to_string(),
        },
        Model {
            id: "claude-3-sonnet-20240229".to_string(),
            object: "model".to_string(),
            created: 1708046800,
            owned_by: "anthropic".to_string(),
        },
        Model {
            id: "claude-3-opus-20240229".to_string(),
            object: "model".to_string(),
            created: 1708046800,
            owned_by: "anthropic".to_string(),
        },
        Model {
            id: "claude-3-haiku-20240307".to_string(),
            object: "model".to_string(),
            created: 1709788800,
            owned_by: "anthropic".to_string(),
        },
    ]
}

pub async fn models_handler(
    State(_config_manager): State<Arc<ConfigManager>>,
) -> AxumResult<impl IntoResponse> {
    info!("Handling GET /api/v1/models request");
    
    // 尝试从LLM配置文件中获取自定义模型
    let models = match get_models_from_config().await {
        Ok(custom_models) => custom_models,
        Err(_) => {
            // 如果无法读取配置文件，返回默认模型列表
            info!("Using default model list");
            get_default_models()
        }
    };
    
    let response = ModelsResponse {
        data: models,
        object: "list".to_string(),
    };
    
    Ok((StatusCode::OK, Json(response)))
}

async fn get_models_from_config() -> Result<Vec<Model>, Box<dyn std::error::Error>> {
    use std::fs;
    use serde_json::Value;
    
    // 尝试读取LLM配置文件
    if let Ok(config_content) = fs::read_to_string(".llm_config") {
        if let Ok(config_json) = serde_json::from_str::<Value>(&config_content) {
            // 如果配置中包含模型信息，使用它
            if let Some(model_id) = config_json.get("model").and_then(|m| m.as_str()) {
                let provider = config_json.get("provider")
                    .and_then(|p| p.as_str())
                    .unwrap_or("custom");
                
                let custom_model = Model {
                    id: model_id.to_string(),
                    object: "model".to_string(),
                    created: chrono::Utc::now().timestamp(),
                    owned_by: provider.to_string(),
                };
                
                // 返回自定义模型加上一些默认模型
                let mut models = vec![custom_model];
                models.extend(get_default_models().into_iter().filter(|m| m.id != model_id));
                return Ok(models);
            }
        }
    }
    
    // 如果没有找到配置，返回错误以便使用默认模型列表
    Err("No config found".into())
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_models_response_structure() {
        let response = ModelsResponse {
            data: vec![Model {
                id: "test-model".to_string(),
                object: "model".to_string(),
                created: **********,
                owned_by: "test".to_string(),
            }],
            object: "list".to_string(),
        };
        
        assert_eq!(response.object, "list");
        assert_eq!(response.data.len(), 1);
        assert_eq!(response.data[0].id, "test-model");
    }

    #[test]
    fn test_default_models_not_empty() {
        let models = get_default_models();
        assert!(!models.is_empty());
        assert!(models.iter().any(|m| m.id.contains("gpt")));
        assert!(models.iter().any(|m| m.id.contains("claude")));
    }

    #[test]
    fn test_model_structure() {
        let model = Model {
            id: "test".to_string(),
            object: "model".to_string(),
            created: **********,
            owned_by: "test".to_string(),
        };
        
        assert_eq!(model.id, "test");
        assert_eq!(model.object, "model");
        assert_eq!(model.created, **********);
        assert_eq!(model.owned_by, "test");
    }
}