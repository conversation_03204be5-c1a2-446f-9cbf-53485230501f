use axum::{
    extract::State,
    http::StatusCode,
    response::IntoResponse,
    Json,
};
use serde::{Deserialize, Serialize};
use std::sync::Arc;
use std::fs;
use std::path::Path;
use bcrypt::{hash, DEFAULT_COST};
use tracing::{info, error};

use crate::config::config_manager::ConfigManager;
use crate::error::result::{Result as CocoResult, AxumResult};
use crate::error::error::CocoError;

// 初始化请求模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SetupConfig {
    pub name: String,
    pub email: String,
    pub password: String,
    pub llm: Option<LLMConfig>,
    pub language: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LLMConfig {
    pub provider: String,
    pub model: String,
    pub api_key: String,
    pub base_url: Option<String>,
}

// 初始化响应模型
#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize)]
pub struct InitializeResponse {
    pub status: String,
    pub message: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ServerInfo {
    pub name: String,
    pub version: String,
    pub initialized: bool,
}

pub async fn initialize_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(payload): Json<SetupConfig>,
) -> AxumResult<impl IntoResponse> {
    info!("Handling POST /setup/_initialize request");
    
    // 验证密码不为空
    if payload.password.is_empty() {
        return Ok((
            StatusCode::BAD_REQUEST,
            Json(InitializeResponse {
                status: "error".to_string(),
                message: Some("密码不能为空".to_string()),
            }),
        ));
    }
    
    // 实际的初始化逻辑
    match perform_initialization(config_manager, payload).await {
        Ok(_) => {
            let response = InitializeResponse {
                status: "ok".to_string(),
                message: Some("初始化完成".to_string()),
            };
            Ok((StatusCode::OK, Json(response)))
        }
        Err(e) => {
            error!("Initialization failed: {}", e);
            let message = match e.to_string().as_str() {
                "already_initialized" => "服务器已经初始化过".to_string(),
                _ => "初始化失败".to_string(),
            };
            Ok((
                StatusCode::BAD_REQUEST,
                Json(InitializeResponse {
                    status: "error".to_string(),
                    message: Some(message),
                }),
            ))
        }
    }
}

async fn perform_initialization(
    _config_manager: Arc<ConfigManager>,
    payload: SetupConfig,
) -> CocoResult<()> {
    // 检查是否已初始化
    let setup_lock_path = ".setup_done";
    if Path::new(setup_lock_path).exists() {
        return Err(CocoError::ServerError("already_initialized".to_string()));
    }
    
    info!("Starting server initialization...");
    
    // 1. 创建密码文件
    info!("Creating password file...");
    let hashed_password = hash(payload.password, DEFAULT_COST)?;
    
    // 保存密码到文件
    fs::write(".password", hashed_password)?;
    
    // 2. 创建用户配置文件
    info!("Creating user profile...");
    let user_profile = serde_json::json!({
        "name": payload.name,
        "email": payload.email,
        "language": payload.language.unwrap_or_else(|| "zh-CN".to_string()),
        "created_at": chrono::Utc::now().to_rfc3339(),
    });
    
    fs::write(".user_profile", serde_json::to_string_pretty(&user_profile)?)?;
    
    // 3. 保存LLM配置
    if let Some(llm_config) = payload.llm {
        info!("Saving LLM configuration...");
        let llm_config_json = serde_json::json!({
            "provider": llm_config.provider,
            "model": llm_config.model,
            "api_key": llm_config.api_key,
            "base_url": llm_config.base_url,
        });
        fs::write(".llm_config", serde_json::to_string_pretty(&llm_config_json)?)?;
    }
    
    // 4. 创建服务器名称文件
    info!("Setting server name...");
    fs::write(".server_name", &payload.name)?;
    
    // 5. 创建初始化锁文件
    info!("Creating initialization lock...");
    fs::write(setup_lock_path, chrono::Utc::now().to_rfc3339())?;
    
    info!("Server initialization completed successfully");
    
    Ok(())
}