use axum::{
    body::Body,
    extract::Path,
    http::{header, HeaderMap, HeaderValue, Request},
    response::{IntoResponse, Response},
};
use std::path::Path as StdPath;
use tokio::fs;

/// 静态文件处理器
pub async fn static_file_handler(
    req: Request<Body>,
) -> impl IntoResponse {
    // 从请求URI中获取文件路径
    let uri = req.uri();
    let path = uri.path();
    
    // 提取文件路径（去掉开头的斜杠）
    let file = if path == "/" {
        "index.html".to_string()
    } else {
        path.trim_start_matches('/').to_string()
    };
    
    // 确定文件路径（指向构建好的前端文件）
    let file_path = StdPath::new("../.public").join(&file);
    
    // 添加调试信息
    tracing::info!("Requested file: {}, initial path: {:?}", file, file_path);
    
    // 如果请求的是目录，则默认返回index.html
    let file_path = if file_path.is_dir() {
        tracing::info!("Path is directory, adding index.html");
        file_path.join("index.html")
    } else {
        file_path
    };
    
    // 对于SPA，如果文件不存在，返回index.html处理路由
    let file_exists = file_path.exists();
    tracing::info!("File exists: {}", file_exists);
    let file_path = if !file_exists {
        tracing::info!("File not found, returning index.html for SPA routing");
        StdPath::new("../.public/index.html").to_path_buf()
    } else {
        file_path
    };
    
    // 读取文件内容
    let content = match fs::read(&file_path).await {
        Ok(content) => content,
        Err(_) => {
            let mut headers = HeaderMap::new();
            headers.insert(header::CONTENT_TYPE, "text/plain".parse().unwrap());
            return Response::builder()
                .status(axum::http::StatusCode::INTERNAL_SERVER_ERROR)
                .body(Body::from("Failed to read file"))
                .unwrap()
                .into_response();
        }
    };
    
    // 根据文件扩展名确定内容类型
    let content_type = get_content_type(&file_path);
    
    // 添加调试信息
    tracing::info!("Serving static file: {:?}, content_type: {}", file_path, content_type);
    
    // 构建响应
    Response::builder()
        .status(axum::http::StatusCode::OK)
        .header(header::CONTENT_TYPE, content_type)
        .body(Body::from(content))
        .unwrap()
        .into_response()
}

/// 根据文件扩展名获取内容类型
fn get_content_type(file_path: &StdPath) -> String {
    if let Some(extension) = file_path.extension() {
        match extension.to_str() {
            Some("html") => "text/html".to_string(),
            Some("css") => "text/css".to_string(),
            Some("js") => "application/javascript".to_string(),
            Some("json") => "application/json".to_string(),
            Some("png") => "image/png".to_string(),
            Some("jpg") | Some("jpeg") => "image/jpeg".to_string(),
            Some("gif") => "image/gif".to_string(),
            Some("svg") => "image/svg+xml".to_string(),
            Some("ico") => "image/x-icon".to_string(),
            Some("woff") => "font/woff".to_string(),
            Some("woff2") => "font/woff2".to_string(),
            Some("ttf") => "font/ttf".to_string(),
            Some("eot") => "application/vnd.ms-fontobject".to_string(),
            Some("txt") => "text/plain".to_string(),
            Some("xml") => "application/xml".to_string(),
            _ => "application/octet-stream".to_string(),
        }
    } else {
        "application/octet-stream".to_string()
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::path::PathBuf;

    #[test]
    fn test_get_content_type() {
        assert_eq!(get_content_type(&PathBuf::from("index.html")), "text/html");
        assert_eq!(get_content_type(&PathBuf::from("style.css")), "text/css");
        assert_eq!(get_content_type(&PathBuf::from("script.js")), "application/javascript");
        assert_eq!(get_content_type(&PathBuf::from("data.json")), "application/json");
        assert_eq!(get_content_type(&PathBuf::from("image.png")), "image/png");
        assert_eq!(get_content_type(&PathBuf::from("photo.jpg")), "image/jpeg");
        assert_eq!(get_content_type(&PathBuf::from("unknown.xyz")), "application/octet-stream");
    }
}