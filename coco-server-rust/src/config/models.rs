use crate::database::DatabaseConfig;
use serde::{Deserialize, Serialize};

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct Config {
    pub env: Option<EnvConfig>,
    pub coco: Option<CocoConfig>,
    pub database: Option<DatabaseConfig>,
    // 其他配置项可以根据需要添加
}

#[derive(Debug, <PERSON><PERSON>, Serialize, Deserialize, Default)]
pub struct EnvConfig {
    // SurrealDB配置
    #[serde(rename = "SURREALDB_URL")]
    pub surrealdb_url: Option<String>,
    #[serde(rename = "SURREALDB_NAMESPACE")]
    pub surrealdb_namespace: Option<String>,
    #[serde(rename = "SURREALDB_DATABASE")]
    pub surrealdb_database: Option<String>,
    #[serde(rename = "SURREALDB_USERNAME")]
    pub surrealdb_username: Option<String>,
    #[serde(rename = "SURREALDB_PASSWORD")]
    pub surrealdb_password: Option<String>,

    // 服务器配置
    #[serde(rename = "WEB_BINDING")]
    pub web_binding: Option<String>,
    #[serde(rename = "API_BINDING")]
    pub api_binding: Option<String>,

    // 保留ES配置以便迁移期间使用
    #[serde(rename = "ES_ENDPOINT")]
    pub es_endpoint: Option<String>,
    #[serde(rename = "ES_USERNAME")]
    pub es_username: Option<String>,
    #[serde(rename = "ES_PASSWORD")]
    pub es_password: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct CocoConfig {
    pub server: Option<CocoServerConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct CocoServerConfig {
    pub public: Option<bool>,
    pub name: Option<String>,
    #[serde(rename = "encode_icon_to_base64")]
    pub encode_icon_to_base64: Option<bool>,
    #[serde(rename = "minimal_client_version")]
    pub minimal_client_version: Option<VersionConfig>,
    pub provider: Option<ProviderConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct VersionConfig {
    pub number: Option<String>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct ProviderConfig {
    pub name: Option<String>,
    pub description: Option<String>,
    pub icon: Option<String>,
    pub website: Option<String>,
    #[serde(rename = "eula")]
    pub eula: Option<String>,
    #[serde(rename = "privacy_policy")]
    pub privacy_policy: Option<String>,
    pub banner: Option<String>,
    #[serde(rename = "auth_provider")]
    pub auth_provider: Option<AuthProviderConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct AuthProviderConfig {
    pub sso: Option<SsoConfig>,
}

#[derive(Debug, Clone, Serialize, Deserialize, Default)]
pub struct SsoConfig {
    pub url: Option<String>,
}
