use crate::config::config_manager::ConfigManager;
use crate::handlers::info_handler::HealthInfo;
use anyhow::Result;
use reqwest;
use std::time::Duration;
use tracing::{debug, error, info, warn};

pub struct HealthChecker {
    client: reqwest::Client,
    config_manager: ConfigManager,
}

impl HealthChecker {
    pub fn new(config_manager: ConfigManager) -> Result<Self> {
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(5))
            .build()?;

        Ok(HealthChecker {
            client,
            config_manager,
        })
    }

    pub async fn check_health(&self) -> HealthInfo {
        use std::collections::HashMap;
        
        let mut services = HashMap::new();
        let mut overall_status = "green".to_string();

        // 暂时禁用Elasticsearch检查，直接返回green状态
        // TODO: 后续可以在客户端实现Elasticsearch检查功能
        info!("Elasticsearch check disabled - returning green status");
        services.insert("Elasticsearch".to_string(), "green".to_string());

        // 暂时禁用数据库检查，直接返回green状态
        // TODO: 后续可以在客户端实现数据库检查功能
        info!("Database check disabled - returning green status");
        services.insert("Database".to_string(), "green".to_string());

        // 暂时禁用API服务检查，直接返回green状态
        // TODO: 后续可以在客户端实现API服务检查功能
        info!("API service check disabled - returning green status");
        services.insert("API Service".to_string(), "green".to_string());

        // 检查系统资源
        let system_status = self.check_system_resources().await;
        if system_status != "green" {
            if overall_status == "green" {
                overall_status = "yellow".to_string();
            }
        }
        services.insert("System Resources".to_string(), system_status);

        HealthInfo {
            status: overall_status,
            services,
        }
    }

    async fn check_elasticsearch(&self) -> String {
        // 从配置中获取Elasticsearch的地址
        let config = self.config_manager.get_config();
        let es_url = config
            .env
            .as_ref()
            .and_then(|env| env.es_endpoint.clone())
            .unwrap_or_else(|| "http://localhost:9200".to_string());

        debug!("Checking Elasticsearch health at: {}", es_url);

        // 发送请求到Elasticsearch的健康检查端点
        match self
            .client
            .get(&format!("{}/_cluster/health", es_url))
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    // 解析响应以获取更详细的健康状态
                    match response.json::<serde_json::Value>().await {
                        Ok(json) => {
                            let status = json
                                .get("status")
                                .and_then(|s| s.as_str())
                                .unwrap_or("green");
                            info!("Elasticsearch health status: {}", status);
                            status.to_lowercase()
                        }
                        Err(e) => {
                            error!("Failed to parse Elasticsearch health response: {}", e);
                            "red".to_string()
                        }
                    }
                } else {
                    error!(
                        "Elasticsearch health check failed with status: {}",
                        response.status()
                    );
                    "red".to_string()
                }
            }
            Err(e) => {
                error!("Failed to connect to Elasticsearch: {}", e);
                "red".to_string()
            }
        }
    }

    async fn check_database(&self) -> String {
        // 检查存储系统的健康状态（在这个项目中是Elasticsearch）
        // 我们可以通过检查Elasticsearch的特定索引或功能来实现这一点
        
        let config = self.config_manager.get_config();
        let es_url = config
            .env
            .as_ref()
            .and_then(|env| env.es_endpoint.clone())
            .unwrap_or_else(|| "http://localhost:9200".to_string());

        debug!("Checking storage system health at: {}", es_url);

        // 首先检查基本连接
        match self
            .client
            .get(&es_url)
            .send()
            .await
        {
            Ok(response) => {
                if !response.status().is_success() {
                    error!(
                        "Storage system connection failed with status: {}",
                        response.status()
                    );
                    return "red".to_string();
                }
            }
            Err(e) => {
                error!("Failed to connect to storage system: {}", e);
                return "red".to_string();
            }
        }

        // 检查Elasticsearch的_cat/indices端点，确保能够列出索引
        match self
            .client
            .get(&format!("{}/_cat/indices", es_url))
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    // 进一步检查关键索引是否存在
                    match self
                        .client
                        .get(&format!("{}/_cat/indices/coco_*", es_url))
                        .send()
                        .await
                    {
                        Ok(index_response) => {
                            if index_response.status().is_success() {
                                info!("Storage system health check passed");
                                "green".to_string()
                            } else {
                                warn!(
                                    "Storage system health check warning - coco indices not found: {}",
                                    index_response.status()
                                );
                                "yellow".to_string()
                            }
                        }
                        Err(e) => {
                            warn!("Failed to check coco indices: {}", e);
                            "yellow".to_string()
                        }
                    }
                } else {
                    error!(
                        "Storage system health check failed with status: {}",
                        response.status()
                    );
                    "red".to_string()
                }
            }
            Err(e) => {
                error!("Failed to connect to storage system: {}", e);
                "red".to_string()
            }
        }
    }

    /// 检查API服务的健康状态
    async fn check_api_service(&self) -> String {
        // 检查关键API端点的可用性
        let config = self.config_manager.get_config();
        let es_url = config
            .env
            .as_ref()
            .and_then(|env| env.es_endpoint.clone())
            .unwrap_or_else(|| "http://localhost:9200".to_string());

        debug!("Checking API service health at: {}", es_url);

        // 检查Elasticsearch的_cluster/stats端点，获取集群统计信息
        match self
            .client
            .get(&format!("{}/_cluster/stats", es_url))
            .send()
            .await
        {
            Ok(response) => {
                if response.status().is_success() {
                    info!("API service health check passed");
                    "green".to_string()
                } else if response.status().as_u16() >= 400 && response.status().as_u16() < 500 {
                    warn!(
                        "API service health check warning with status: {}",
                        response.status()
                    );
                    "yellow".to_string()
                } else {
                    error!(
                        "API service health check failed with status: {}",
                        response.status()
                    );
                    "red".to_string()
                }
            }
            Err(e) => {
                error!("Failed to connect to API service: {}", e);
                "red".to_string()
            }
        }
    }

    /// 检查系统资源使用情况
    async fn check_system_resources(&self) -> String {
        // 在这个简单的实现中，我们返回GREEN
        // 在更复杂的实现中，我们可以检查内存、CPU、磁盘使用情况等
        debug!("Checking system resources");
        
        // 这里可以添加实际的系统资源检查逻辑
        // 例如：检查可用内存、CPU使用率、磁盘空间等
        
        info!("System resources check completed");
        "green".to_string()
    }
}