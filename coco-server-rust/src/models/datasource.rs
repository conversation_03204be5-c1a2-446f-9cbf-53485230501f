use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;

use super::connector::ConnectorConfig;

/// DataSource 数据源模型
///
/// 与Go版本完全兼容的数据源结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSource {
    /// 数据源唯一标识符
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<String>,

    /// 数据源类型，如 "connector"
    #[serde(rename = "type")]
    pub r#type: String,

    /// 显示名称
    pub name: String,

    /// 描述信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// 图标
    #[serde(skip_serializing_if = "Option::is_none")]
    pub icon: Option<String>,

    /// 分类
    #[serde(skip_serializing_if = "Option::is_none")]
    pub category: Option<String>,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,

    /// 连接器配置
    #[serde(skip_serializing_if = "Option::is_none")]
    pub connector: Option<ConnectorConfig>,

    /// 是否允许同步
    #[serde(default = "default_sync_enabled")]
    pub sync_enabled: bool,

    /// 是否启用
    #[serde(default = "default_enabled")]
    pub enabled: bool,

    /// 创建时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created: Option<DateTime<Utc>>,

    /// 更新时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated: Option<DateTime<Utc>>,
}

/// 创建数据源请求模型
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateDataSourceRequest {
    /// 显示名称
    pub name: String,

    /// 数据源类型
    #[serde(rename = "type")]
    pub r#type: String,

    /// 描述信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// 分类
    #[serde(skip_serializing_if = "Option::is_none")]
    pub category: Option<String>,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,

    /// 连接器配置
    pub connector: ConnectorConfig,

    /// 是否允许同步
    #[serde(default = "default_sync_enabled")]
    pub sync_enabled: bool,

    /// 是否启用
    #[serde(default = "default_enabled")]
    pub enabled: bool,
}

/// 更新数据源请求模型
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateDataSourceRequest {
    /// 显示名称
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,

    /// 数据源类型
    #[serde(rename = "type", skip_serializing_if = "Option::is_none")]
    pub r#type: Option<String>,

    /// 描述信息
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// 分类
    #[serde(skip_serializing_if = "Option::is_none")]
    pub category: Option<String>,

    /// 标签
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,

    /// 连接器配置
    #[serde(skip_serializing_if = "Option::is_none")]
    pub connector: Option<ConnectorConfig>,

    /// 是否允许同步
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sync_enabled: Option<bool>,

    /// 是否启用
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
}

/// 创建数据源响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateDataSourceResponse {
    /// 创建的数据源ID
    #[serde(rename = "_id")]
    pub id: String,

    /// 操作结果
    pub result: String,
}

/// 获取数据源响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct GetDataSourceResponse {
    /// 是否找到
    pub found: bool,

    /// 数据源ID
    #[serde(rename = "_id")]
    pub id: String,

    /// 数据源数据
    #[serde(rename = "_source")]
    pub source: DataSource,
}

/// 更新数据源响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateDataSourceResponse {
    /// 数据源ID
    #[serde(rename = "_id")]
    pub id: String,

    /// 操作结果
    pub result: String,
}

/// 删除数据源响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct DeleteDataSourceResponse {
    /// 数据源ID
    #[serde(rename = "_id")]
    pub id: String,

    /// 操作结果
    pub result: String,
}

/// 搜索响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchResponse<T = DataSource> {
    /// 查询耗时（毫秒）
    pub took: u64,

    /// 是否超时
    pub timed_out: bool,

    /// 搜索结果
    pub hits: SearchHits<T>,
}

/// 搜索结果集合
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchHits<T = DataSource> {
    /// 总数信息
    pub total: SearchTotal,

    /// 最高分数
    pub max_score: Option<f64>,

    /// 结果列表
    pub hits: Vec<SearchHit<T>>,
}

/// 搜索总数信息
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchTotal {
    /// 总数值
    pub value: u64,

    /// 关系类型（eq, gte等）
    pub relation: String,
}

/// 单个搜索结果
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchHit<T = DataSource> {
    /// 索引名称
    #[serde(rename = "_index")]
    pub _index: String,

    /// 文档类型
    #[serde(rename = "_type")]
    pub _type: String,

    /// 文档ID
    #[serde(rename = "_id")]
    pub _id: String,

    /// 相关性分数
    #[serde(rename = "_score")]
    pub _score: Option<f64>,

    /// 源数据
    #[serde(rename = "_source")]
    pub _source: T,
}

/// 搜索查询参数
#[derive(Debug, Serialize, Deserialize)]
pub struct SearchQuery {
    /// 查询字符串
    #[serde(skip_serializing_if = "Option::is_none")]
    pub q: Option<String>,

    /// 分页大小
    #[serde(skip_serializing_if = "Option::is_none")]
    pub size: Option<u64>,

    /// 分页偏移
    #[serde(skip_serializing_if = "Option::is_none")]
    pub from: Option<u64>,

    /// 排序字段
    #[serde(skip_serializing_if = "Option::is_none")]
    pub sort: Option<String>,

    /// 其他查询参数
    #[serde(flatten)]
    pub extra: std::collections::HashMap<String, Value>,
}

// 默认值函数
fn default_sync_enabled() -> bool {
    true
}

fn default_enabled() -> bool {
    true
}

impl DataSource {
    /// 创建新的数据源实例
    pub fn new(name: String, r#type: String) -> Self {
        Self {
            id: None,
            r#type,
            name,
            description: None,
            icon: None,
            category: None,
            tags: None,
            connector: None,
            sync_enabled: true,
            enabled: true,
            created: None,
            updated: None,
        }
    }

    /// 设置ID并返回新实例
    pub fn with_id(mut self, id: String) -> Self {
        self.id = Some(id);
        self
    }

    /// 设置时间戳
    pub fn with_timestamps(mut self) -> Self {
        let now = Utc::now();
        self.created = Some(now);
        self.updated = Some(now);
        self
    }

    /// 更新时间戳
    pub fn touch(mut self) -> Self {
        self.updated = Some(Utc::now());
        self
    }
}

impl From<CreateDataSourceRequest> for DataSource {
    fn from(request: CreateDataSourceRequest) -> Self {
        Self {
            id: None,
            r#type: request.r#type,
            name: request.name,
            description: request.description,
            icon: None,
            category: request.category,
            tags: request.tags,
            connector: Some(request.connector),
            sync_enabled: request.sync_enabled,
            enabled: request.enabled,
            created: None,
            updated: None,
        }
    }
}
