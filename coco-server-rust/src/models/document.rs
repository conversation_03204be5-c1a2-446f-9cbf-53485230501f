use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use serde_json::Value;

/// 文档模型
/// 
/// 用于在数据源中存储的文档结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Document {
    /// 文档ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<String>,
    
    /// 所属数据源ID
    pub datasource_id: String,
    
    /// 文档标题
    #[serde(skip_serializing_if = "Option::is_none")]
    pub title: Option<String>,
    
    /// 文档内容
    #[serde(skip_serializing_if = "Option::is_none")]
    pub content: Option<String>,
    
    /// 文档元数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<Value>,
    
    /// 创建时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created: Option<DateTime<Utc>>,
    
    /// 更新时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated: Option<DateTime<Utc>>,
}

/// 创建文档请求模型
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateDocumentRequest {
    /// 文档标题
    #[serde(skip_serializing_if = "Option::is_none")]
    pub title: Option<String>,
    
    /// 文档内容
    #[serde(skip_serializing_if = "Option::is_none")]
    pub content: Option<String>,
    
    /// 文档元数据
    #[serde(skip_serializing_if = "Option::is_none")]
    pub metadata: Option<Value>,
}

/// 创建文档响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateDocumentResponse {
    /// 创建的文档ID
    #[serde(rename = "_id")]
    pub id: String,
    
    /// 操作结果
    pub result: String,
}

impl Document {
    /// 创建新的文档实例
    pub fn new(datasource_id: String) -> Self {
        Self {
            id: None,
            datasource_id,
            title: None,
            content: None,
            metadata: None,
            created: None,
            updated: None,
        }
    }
    
    /// 设置ID并返回新实例
    pub fn with_id(mut self, id: String) -> Self {
        self.id = Some(id);
        self
    }
    
    /// 设置时间戳
    pub fn with_timestamps(mut self) -> Self {
        let now = Utc::now();
        self.created = Some(now);
        self.updated = Some(now);
        self
    }
}

impl From<CreateDocumentRequest> for Document {
    fn from(request: CreateDocumentRequest) -> Self {
        Self {
            id: None,
            datasource_id: String::new(), // 需要在创建时设置
            title: request.title,
            content: request.content,
            metadata: request.metadata,
            created: None,
            updated: None,
        }
    }
}
