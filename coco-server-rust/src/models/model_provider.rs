use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};

/// Model Provider 主要数据结构
/// 对应Go版本的common.ModelProvider
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelProvider {
    pub id: String,
    pub created: DateTime<Utc>,
    pub updated: DateTime<Utc>,
    pub name: String,
    pub api_key: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<ModelConfig>,
    pub enabled: bool,
    pub builtin: bool,
    pub description: String,
}

/// 模型配置结构
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub settings: Option<ModelSettings>,
}

/// 模型设置参数
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelSettings {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub temperature: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub top_p: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub presence_penalty: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub frequency_penalty: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_tokens: Option<i32>,
}

/// 创建模型提供商请求
#[derive(Debug, Deserialize)]
pub struct CreateModelProviderRequest {
    pub name: String,
    pub api_key: String,
    pub api_type: String,
    pub base_url: String,
    #[serde(default)]
    pub icon: String,
    #[serde(default)]
    pub models: Vec<ModelConfig>,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    #[serde(default)]
    pub description: String,
}

/// 更新模型提供商请求
#[derive(Debug, Deserialize)]
pub struct UpdateModelProviderRequest {
    pub name: Option<String>,
    pub api_key: Option<String>,
    pub api_type: Option<String>,
    pub base_url: Option<String>,
    pub icon: Option<String>,
    pub models: Option<Vec<ModelConfig>>,
    pub enabled: Option<bool>,
    pub description: Option<String>,
}

/// API响应结构，兼容Go版本格式
#[derive(Debug, Serialize)]
pub struct ApiResponse<T> {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub data: Option<T>,
}

/// 搜索查询参数
#[derive(Debug, Deserialize)]
pub struct SearchParams {
    #[serde(default)]
    pub size: Option<usize>,
    #[serde(default)]
    pub from: Option<usize>,
    #[serde(default)]
    pub sort: Option<String>,
    #[serde(default)]
    pub q: Option<String>, // 查询字符串
}

/// 搜索响应结构，兼容Elasticsearch格式
#[derive(Debug, Serialize)]
pub struct ModelProviderSearchResponse {
    pub took: u64,
    pub timed_out: bool,
    pub hits: ModelProviderSearchHits,
}

#[derive(Debug, Serialize)]
pub struct ModelProviderSearchHits {
    pub total: ModelProviderSearchTotal,
    pub max_score: Option<f64>,
    pub hits: Vec<ModelProviderSearchHit>,
}

#[derive(Debug, Serialize)]
pub struct ModelProviderSearchTotal {
    pub value: usize,
    pub relation: String,
}

#[derive(Debug, Serialize)]
pub struct ModelProviderSearchHit {
    #[serde(rename = "_index")]
    pub index: String,
    #[serde(rename = "_type")]
    pub doc_type: String,
    #[serde(rename = "_id")]
    pub id: String,
    #[serde(rename = "_score")]
    pub score: Option<f64>,
    #[serde(rename = "_source")]
    pub source: ModelProvider,
}

// 默认值函数
fn default_enabled() -> bool {
    true
}

impl ModelProvider {
    /// 创建新的ModelProvider实例
    pub fn new(req: CreateModelProviderRequest) -> Self {
        let now = Utc::now();
        Self {
            id: uuid::Uuid::new_v4().to_string(),
            created: now,
            updated: now,
            name: req.name,
            api_key: req.api_key,
            api_type: req.api_type,
            base_url: req.base_url,
            icon: req.icon,
            models: req.models,
            enabled: req.enabled,
            builtin: false, // 新创建的都不是内置的
            description: req.description,
        }
    }

    /// 更新ModelProvider
    pub fn update(&mut self, req: UpdateModelProviderRequest) {
        if let Some(name) = req.name {
            self.name = name;
        }
        if let Some(api_key) = req.api_key {
            self.api_key = api_key;
        }
        if let Some(api_type) = req.api_type {
            self.api_type = api_type;
        }
        if let Some(base_url) = req.base_url {
            self.base_url = base_url;
        }
        if let Some(icon) = req.icon {
            self.icon = icon;
        }
        if let Some(models) = req.models {
            self.models = models;
        }
        if let Some(enabled) = req.enabled {
            self.enabled = enabled;
        }
        if let Some(description) = req.description {
            self.description = description;
        }
        self.updated = Utc::now();
    }

    /// 过滤敏感字段，用于API响应
    pub fn sanitize(&self) -> serde_json::Value {
        let mut value = serde_json::to_value(self).unwrap();
        if let Some(obj) = value.as_object_mut() {
            // 在某些情况下可能需要过滤API密钥
            // obj.remove("api_key");
        }
        value
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_model_provider_creation() {
        let req = CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        };

        let provider = ModelProvider::new(req);
        assert_eq!(provider.name, "Test Provider");
        assert_eq!(provider.api_type, "openai");
        assert!(!provider.builtin);
        assert!(provider.enabled);
    }

    #[test]
    fn test_model_provider_update() {
        let req = CreateModelProviderRequest {
            name: "Test Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![],
            enabled: true,
            description: "Test description".to_string(),
        };

        let mut provider = ModelProvider::new(req);
        let original_updated = provider.updated;

        // 等待一毫秒确保时间戳不同
        std::thread::sleep(std::time::Duration::from_millis(1));

        let update_req = UpdateModelProviderRequest {
            name: Some("Updated Provider".to_string()),
            api_key: None,
            api_type: None,
            base_url: None,
            icon: None,
            models: None,
            enabled: Some(false),
            description: None,
        };

        provider.update(update_req);
        assert_eq!(provider.name, "Updated Provider");
        assert!(!provider.enabled);
        assert!(provider.updated > original_updated);
    }

    #[test]
    fn test_serialization() {
        let provider = ModelProvider {
            id: "test-id".to_string(),
            created: Utc::now(),
            updated: Utc::now(),
            name: "Test Provider".to_string(),
            api_key: "test-key".to_string(),
            api_type: "openai".to_string(),
            base_url: "https://api.test.com".to_string(),
            icon: "test-icon".to_string(),
            models: vec![ModelConfig {
                name: "test-model".to_string(),
                settings: Some(ModelSettings {
                    temperature: Some(0.8),
                    top_p: Some(0.9),
                    presence_penalty: None,
                    frequency_penalty: None,
                    max_tokens: Some(1024),
                }),
            }],
            enabled: true,
            builtin: false,
            description: "Test description".to_string(),
        };

        // 测试序列化
        let json = serde_json::to_string(&provider).unwrap();
        assert!(json.contains("Test Provider"));

        // 测试反序列化
        let deserialized: ModelProvider = serde_json::from_str(&json).unwrap();
        assert_eq!(deserialized.name, provider.name);
        assert_eq!(deserialized.models.len(), 1);
    }
}
