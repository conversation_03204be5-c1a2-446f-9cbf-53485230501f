use serde::{Deserialize, Serialize};
use serde_json::Value;

/// MCP Server 模型
///
/// 与Go版本完全兼容的MCP Server结构体
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct MCPServer {
    /// MCP Server ID
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<String>,

    /// MCP Server 名称
    pub name: String,

    /// MCP Server 描述
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// MCP Server 类型
    #[serde(rename = "type", skip_serializing_if = "Option::is_none")]
    pub server_type: Option<String>,

    /// MCP Server 配置
    #[serde(skip_serializing_if = "Option::is_none")]
    pub config: Option<Value>,

    /// 是否启用
    #[serde(default = "default_enabled")]
    pub enabled: bool,

    /// 创建时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created: Option<String>,

    /// 更新时间
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated: Option<String>,
}

fn default_enabled() -> bool {
    true
}

impl MCPServer {
    /// 创建新的MCP Server实例
    pub fn new(name: String) -> Self {
        Self {
            id: None,
            name,
            description: None,
            server_type: None,
            config: None,
            enabled: true,
            created: None,
            updated: None,
        }
    }

    /// 设置ID
    pub fn with_id(mut self, id: String) -> Self {
        self.id = Some(id);
        self
    }

    /// 设置描述
    pub fn with_description(mut self, description: String) -> Self {
        self.description = Some(description);
        self
    }

    /// 设置类型
    pub fn with_type(mut self, server_type: String) -> Self {
        self.server_type = Some(server_type);
        self
    }

    /// 设置配置
    pub fn with_config(mut self, config: Value) -> Self {
        self.config = Some(config);
        self
    }

    /// 设置启用状态
    pub fn with_enabled(mut self, enabled: bool) -> Self {
        self.enabled = enabled;
        self
    }
}

/// 创建MCP Server请求模型
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateMCPServerRequest {
    /// MCP Server 名称
    pub name: String,

    /// MCP Server 描述
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// MCP Server 类型
    #[serde(rename = "type", skip_serializing_if = "Option::is_none")]
    pub server_type: Option<String>,

    /// MCP Server 配置
    #[serde(skip_serializing_if = "Option::is_none")]
    pub config: Option<Value>,

    /// 是否启用
    #[serde(default = "default_enabled")]
    pub enabled: bool,
}

/// 更新MCP Server请求模型
#[derive(Debug, Serialize, Deserialize)]
pub struct UpdateMCPServerRequest {
    /// MCP Server 名称
    #[serde(skip_serializing_if = "Option::is_none")]
    pub name: Option<String>,

    /// MCP Server 描述
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,

    /// MCP Server 类型
    #[serde(rename = "type", skip_serializing_if = "Option::is_none")]
    pub server_type: Option<String>,

    /// MCP Server 配置
    #[serde(skip_serializing_if = "Option::is_none")]
    pub config: Option<Value>,

    /// 是否启用
    #[serde(skip_serializing_if = "Option::is_none")]
    pub enabled: Option<bool>,
}
