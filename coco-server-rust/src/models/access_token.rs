use chrono::{DateTime, Utc};
use serde::{Deserialize, Serialize};
use uuid::Uuid;

/// API访问令牌模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessToken {
    /// 令牌ID
    pub id: String,
    /// 访问令牌字符串
    pub access_token: String,
    /// 用户ID
    pub user_id: String,
    /// 令牌名称
    pub name: String,
    /// 提供商类型
    pub provider: String,
    /// 令牌类型
    pub token_type: String,
    /// 用户角色
    pub roles: Vec<String>,
    /// 权限列表
    pub permissions: Vec<String>,
    /// 过期时间戳
    pub expire_in: i64,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后使用时间
    pub last_used: Option<DateTime<Utc>>,
    /// 是否激活
    pub is_active: bool,
}

impl AccessToken {
    /// 创建新的API令牌
    pub fn new(user_id: String, name: String) -> Self {
        let token_value = format!("{}-{}", Uuid::new_v4(), generate_random_string(64));
        let expire_in = Utc::now().timestamp() + 365 * 24 * 3600; // 365天

        Self {
            id: Uuid::new_v4().to_string(),
            access_token: token_value,
            user_id,
            name,
            provider: "access_token".to_string(),
            token_type: "api".to_string(),
            roles: vec!["admin".to_string()], // 默认管理员角色
            permissions: vec![],
            expire_in,
            created_at: Utc::now(),
            last_used: None,
            is_active: true,
        }
    }

    /// 检查令牌是否过期
    pub fn is_expired(&self) -> bool {
        Utc::now().timestamp() > self.expire_in
    }

    /// 更新最后使用时间
    pub fn update_last_used(&mut self) {
        self.last_used = Some(Utc::now());
    }

    /// 撤销令牌
    pub fn revoke(&mut self) {
        self.is_active = false;
    }
}

/// 生成随机字符串
fn generate_random_string(length: usize) -> String {
    use rand::Rng;
    const CHARSET: &[u8] = b"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
    let mut rng = rand::thread_rng();
    
    (0..length)
        .map(|_| {
            let idx = rng.gen_range(0..CHARSET.len());
            CHARSET[idx] as char
        })
        .collect()
}

/// API令牌请求模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTokenRequest {
    /// 令牌名称（可选）
    pub name: Option<String>,
}

/// API令牌响应模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct CreateTokenResponse {
    /// 访问令牌
    pub access_token: String,
    /// 过期时间（秒）
    pub expire_in: i64,
}

/// 令牌列表项模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenListItem {
    /// 令牌ID
    pub id: String,
    /// 令牌名称
    pub name: String,
    /// 创建时间
    pub created_at: DateTime<Utc>,
    /// 最后使用时间
    pub last_used: Option<DateTime<Utc>>,
    /// 是否激活
    pub is_active: bool,
    /// 过期时间戳
    pub expire_in: i64,
}

impl From<AccessToken> for TokenListItem {
    fn from(token: AccessToken) -> Self {
        Self {
            id: token.id,
            name: token.name,
            created_at: token.created_at,
            last_used: token.last_used,
            is_active: token.is_active,
            expire_in: token.expire_in,
        }
    }
}

/// 令牌重命名请求模型
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct RenameTokenRequest {
    /// 新名称
    pub name: String,
}

/// 生成API令牌名称
pub fn generate_api_token_name(prefix: Option<&str>) -> String {
    let prefix = prefix.unwrap_or("token");
    let timestamp = Utc::now().timestamp_millis();
    let random_str = generate_random_string(8);
    format!("{}_{}_{}",prefix, timestamp, random_str)
}
