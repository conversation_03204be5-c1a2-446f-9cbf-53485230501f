use std::collections::HashSet;
use tracing::info;

use crate::config::config_manager::ConfigManager;

pub struct AuthManager {
    valid_tokens: HashSet<String>,
}

impl AuthManager {
    pub fn new(config_manager: &ConfigManager) -> Self {
        let mut valid_tokens = HashSet::new();
        
        // 从配置中读取有效令牌
        let _config = config_manager.get_config();
        
        // 这里应该从配置文件中读取令牌列表
        // 目前我们添加一些默认令牌
        valid_tokens.insert("test-token-1".to_string());
        valid_tokens.insert("test-token-2".to_string());
        valid_tokens.insert("abc123".to_string());
        
        // 也可以从环境变量中读取令牌
        if let Ok(token) = std::env::var("API_TOKEN") {
            if !token.is_empty() {
                info!("Loading API token from environment variable");
                valid_tokens.insert(token);
            }
        }
        
        // 也可以从配置文件的特定部分读取令牌列表
        // 例如：config.auth.tokens
        // 这里需要根据实际的配置文件结构来实现
        
        info!("Loaded {} valid API tokens", valid_tokens.len());
        
        AuthManager { valid_tokens }
    }
    
    pub fn is_valid_token(&self, token: &str) -> bool {
        self.valid_tokens.contains(token)
    }
    
    pub fn add_token(&mut self, token: String) {
        self.valid_tokens.insert(token);
    }
    
    pub fn remove_token(&mut self, token: &str) {
        self.valid_tokens.remove(token);
    }
    
    pub fn list_tokens(&self) -> Vec<String> {
        self.valid_tokens.iter().cloned().collect()
    }
}