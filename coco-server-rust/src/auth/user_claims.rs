use serde::{Deserialize, Serialize};

/// JWT Claims结构，用于JWT令牌的载荷
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserClaims {
    /// 用户ID
    pub user_id: String,
    /// 用户名
    pub username: String,
    /// 用户角色列表
    pub roles: Vec<String>,
    /// 认证提供商
    pub provider: String,
    /// 过期时间（Unix时间戳）
    pub exp: usize,
    /// 签发时间（Unix时间戳）
    pub iat: usize,
}

impl UserClaims {
    /// 创建新的用户声明
    pub fn new(
        user_id: String,
        username: String,
        roles: Vec<String>,
        provider: String,
        exp: usize,
        iat: usize,
    ) -> Self {
        Self {
            user_id,
            username,
            roles,
            provider,
            exp,
            iat,
        }
    }

    /// 检查令牌是否已过期
    pub fn is_expired(&self) -> bool {
        use std::time::{SystemTime, UNIX_EPOCH};
        
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as usize;
        
        now > self.exp
    }

    /// 检查用户是否有指定角色
    pub fn has_role(&self, role: &str) -> bool {
        self.roles.contains(&role.to_string())
    }

    /// 检查用户是否为管理员
    pub fn is_admin(&self) -> bool {
        self.has_role("admin")
    }
}

/// 用户上下文，用于在请求处理过程中传递用户信息
#[derive(Debug, Clone)]
pub struct UserContext {
    /// 用户ID
    pub user_id: String,
    /// 用户名
    pub username: String,
    /// 用户角色列表
    pub roles: Vec<String>,
    /// 认证类型
    pub auth_type: AuthType,
    /// 认证提供商
    pub provider: String,
}

/// 认证类型枚举
#[derive(Debug, Clone, PartialEq)]
pub enum AuthType {
    /// JWT令牌认证
    JWT,
    /// API令牌认证
    ApiToken,
}

impl UserContext {
    /// 从JWT Claims创建用户上下文
    pub fn from_jwt_claims(claims: &UserClaims) -> Self {
        Self {
            user_id: claims.user_id.clone(),
            username: claims.username.clone(),
            roles: claims.roles.clone(),
            auth_type: AuthType::JWT,
            provider: claims.provider.clone(),
        }
    }

    /// 创建API令牌用户上下文
    pub fn from_api_token(
        user_id: String,
        username: String,
        roles: Vec<String>,
        provider: String,
    ) -> Self {
        Self {
            user_id,
            username,
            roles,
            auth_type: AuthType::ApiToken,
            provider,
        }
    }

    /// 检查用户是否有指定角色
    pub fn has_role(&self, role: &str) -> bool {
        self.roles.contains(&role.to_string())
    }

    /// 检查用户是否为管理员
    pub fn is_admin(&self) -> bool {
        self.has_role("admin")
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::time::{SystemTime, UNIX_EPOCH};

    #[test]
    fn test_user_claims_creation() {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as usize;

        let claims = UserClaims::new(
            "user123".to_string(),
            "testuser".to_string(),
            vec!["admin".to_string()],
            "simple".to_string(),
            now + 3600, // 1小时后过期
            now,
        );

        assert_eq!(claims.user_id, "user123");
        assert_eq!(claims.username, "testuser");
        assert!(claims.has_role("admin"));
        assert!(!claims.has_role("user"));
        assert!(claims.is_admin());
        assert!(!claims.is_expired());
    }

    #[test]
    fn test_user_claims_expiration() {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as usize;

        let expired_claims = UserClaims::new(
            "user123".to_string(),
            "testuser".to_string(),
            vec!["user".to_string()],
            "simple".to_string(),
            now - 3600, // 1小时前过期
            now - 7200, // 2小时前签发
        );

        assert!(expired_claims.is_expired());
    }

    #[test]
    fn test_user_context_from_jwt_claims() {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as usize;

        let claims = UserClaims::new(
            "user123".to_string(),
            "testuser".to_string(),
            vec!["admin".to_string()],
            "simple".to_string(),
            now + 3600,
            now,
        );

        let context = UserContext::from_jwt_claims(&claims);

        assert_eq!(context.user_id, "user123");
        assert_eq!(context.username, "testuser");
        assert_eq!(context.auth_type, AuthType::JWT);
        assert!(context.is_admin());
    }

    #[test]
    fn test_user_context_from_api_token() {
        let context = UserContext::from_api_token(
            "api_user".to_string(),
            "apiuser".to_string(),
            vec!["user".to_string()],
            "api_token".to_string(),
        );

        assert_eq!(context.user_id, "api_user");
        assert_eq!(context.auth_type, AuthType::ApiToken);
        assert!(!context.is_admin());
        assert!(context.has_role("user"));
    }
}
