use serde::{Deserialize, Serialize};
use std::collections::HashSet;

/// 权限枚举，对应Go版本中的权限常量
#[derive(Debug, Clone, PartialEq, Eq, Hash, Serialize, Deserialize)]
pub enum Permission {
    /// 创建模型提供商权限
    CreateLLM,
    /// 读取模型提供商权限
    ReadLLM,
    /// 更新模型提供商权限
    UpdateLLM,
    /// 删除模型提供商权限
    DeleteLLM,
    /// 搜索模型提供商权限
    SearchLLM,
}

impl Permission {
    /// 从字符串转换为权限枚举
    pub fn from_str(s: &str) -> Option<Self> {
        match s {
            "createLLMPermission" => Some(Permission::CreateLLM),
            "readLLMPermission" => Some(Permission::ReadLLM),
            "updateLLMPermission" => Some(Permission::UpdateLLM),
            "deleteLLMPermission" => Some(Permission::DeleteLLM),
            "searchLLMPermission" => Some(Permission::SearchLLM),
            _ => None,
        }
    }

    /// 转换为字符串（与Go版本保持一致）
    pub fn to_string(&self) -> &'static str {
        match self {
            Permission::CreateLLM => "createLLMPermission",
            Permission::ReadLLM => "readLLMPermission",
            Permission::UpdateLLM => "updateLLMPermission",
            Permission::DeleteLLM => "deleteLLMPermission",
            Permission::SearchLLM => "searchLLMPermission",
        }
    }
}

/// 用户权限集合
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserPermissions {
    pub permissions: HashSet<Permission>,
}

impl UserPermissions {
    /// 创建新的用户权限集合
    pub fn new() -> Self {
        Self {
            permissions: HashSet::new(),
        }
    }

    /// 从角色列表创建权限集合
    pub fn from_roles(roles: &[String]) -> Self {
        let mut permissions = HashSet::new();
        
        for role in roles {
            match role.as_str() {
                "admin" => {
                    // 管理员拥有所有权限
                    permissions.insert(Permission::CreateLLM);
                    permissions.insert(Permission::ReadLLM);
                    permissions.insert(Permission::UpdateLLM);
                    permissions.insert(Permission::DeleteLLM);
                    permissions.insert(Permission::SearchLLM);
                }
                "user" => {
                    // 普通用户拥有读取和搜索权限
                    permissions.insert(Permission::ReadLLM);
                    permissions.insert(Permission::SearchLLM);
                }
                "editor" => {
                    // 编辑者拥有读取、搜索、创建和更新权限
                    permissions.insert(Permission::ReadLLM);
                    permissions.insert(Permission::SearchLLM);
                    permissions.insert(Permission::CreateLLM);
                    permissions.insert(Permission::UpdateLLM);
                }
                _ => {
                    // 未知角色，不添加任何权限
                }
            }
        }

        Self { permissions }
    }

    /// 检查是否拥有指定权限
    pub fn has_permission(&self, permission: &Permission) -> bool {
        self.permissions.contains(permission)
    }

    /// 添加权限
    pub fn add_permission(&mut self, permission: Permission) {
        self.permissions.insert(permission);
    }

    /// 移除权限
    pub fn remove_permission(&mut self, permission: &Permission) {
        self.permissions.remove(permission);
    }

    /// 检查是否拥有所有指定权限
    pub fn has_all_permissions(&self, permissions: &[Permission]) -> bool {
        permissions.iter().all(|p| self.has_permission(p))
    }

    /// 检查是否拥有任一指定权限
    pub fn has_any_permission(&self, permissions: &[Permission]) -> bool {
        permissions.iter().any(|p| self.has_permission(p))
    }
}

impl Default for UserPermissions {
    fn default() -> Self {
        Self::new()
    }
}

/// 权限检查器
pub struct PermissionChecker;

impl PermissionChecker {
    /// 检查用户是否有权限执行指定操作
    pub fn check_permission(user_permissions: &UserPermissions, required_permission: &Permission) -> bool {
        user_permissions.has_permission(required_permission)
    }

    /// 检查用户是否有权限访问模型提供商API
    pub fn check_model_provider_permission(
        user_permissions: &UserPermissions,
        operation: &str,
    ) -> bool {
        let required_permission = match operation {
            "create" => Permission::CreateLLM,
            "read" | "get" => Permission::ReadLLM,
            "update" | "put" => Permission::UpdateLLM,
            "delete" => Permission::DeleteLLM,
            "search" => Permission::SearchLLM,
            _ => return false,
        };

        Self::check_permission(user_permissions, &required_permission)
    }

    /// 为超级管理员创建完整权限集合
    pub fn create_admin_permissions() -> UserPermissions {
        let mut permissions = HashSet::new();
        permissions.insert(Permission::CreateLLM);
        permissions.insert(Permission::ReadLLM);
        permissions.insert(Permission::UpdateLLM);
        permissions.insert(Permission::DeleteLLM);
        permissions.insert(Permission::SearchLLM);

        UserPermissions { permissions }
    }

    /// 为只读用户创建权限集合
    pub fn create_readonly_permissions() -> UserPermissions {
        let mut permissions = HashSet::new();
        permissions.insert(Permission::ReadLLM);
        permissions.insert(Permission::SearchLLM);

        UserPermissions { permissions }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_permission_from_str() {
        assert_eq!(Permission::from_str("createLLMPermission"), Some(Permission::CreateLLM));
        assert_eq!(Permission::from_str("readLLMPermission"), Some(Permission::ReadLLM));
        assert_eq!(Permission::from_str("invalid"), None);
    }

    #[test]
    fn test_permission_to_string() {
        assert_eq!(Permission::CreateLLM.to_string(), "createLLMPermission");
        assert_eq!(Permission::ReadLLM.to_string(), "readLLMPermission");
    }

    #[test]
    fn test_user_permissions_from_roles() {
        let admin_roles = vec!["admin".to_string()];
        let admin_perms = UserPermissions::from_roles(&admin_roles);
        assert!(admin_perms.has_permission(&Permission::CreateLLM));
        assert!(admin_perms.has_permission(&Permission::DeleteLLM));

        let user_roles = vec!["user".to_string()];
        let user_perms = UserPermissions::from_roles(&user_roles);
        assert!(user_perms.has_permission(&Permission::ReadLLM));
        assert!(!user_perms.has_permission(&Permission::CreateLLM));
    }

    #[test]
    fn test_permission_checker() {
        let admin_perms = PermissionChecker::create_admin_permissions();
        assert!(PermissionChecker::check_model_provider_permission(&admin_perms, "create"));
        assert!(PermissionChecker::check_model_provider_permission(&admin_perms, "delete"));

        let readonly_perms = PermissionChecker::create_readonly_permissions();
        assert!(PermissionChecker::check_model_provider_permission(&readonly_perms, "read"));
        assert!(!PermissionChecker::check_model_provider_permission(&readonly_perms, "create"));
    }

    #[test]
    fn test_has_all_permissions() {
        let admin_perms = PermissionChecker::create_admin_permissions();
        let required = vec![Permission::ReadLLM, Permission::CreateLLM];
        assert!(admin_perms.has_all_permissions(&required));

        let readonly_perms = PermissionChecker::create_readonly_permissions();
        assert!(!readonly_perms.has_all_permissions(&required));
    }

    #[test]
    fn test_has_any_permission() {
        let readonly_perms = PermissionChecker::create_readonly_permissions();
        let permissions = vec![Permission::CreateLLM, Permission::ReadLLM];
        assert!(readonly_perms.has_any_permission(&permissions));

        let empty_perms = UserPermissions::new();
        assert!(!empty_perms.has_any_permission(&permissions));
    }
}
