use crate::error::error::CocoError;
use crate::models::datasource::*;
use crate::models::document::*;
use crate::repositories::datasource_repo::{DataSourceRepository, RepositoryError};
use crate::services::cache_service::CacheService;
use crate::services::validation_service::{ValidationService, ValidationError};
use std::sync::Arc;
use tracing::{debug, error, info, warn};

/// 业务服务错误类型
#[derive(Debug, thiserror::Error)]
pub enum ServiceError {
    #[error("验证失败: {0}")]
    ValidationFailed(String),
    
    #[error("资源不存在: {0}")]
    NotFound(String),
    
    #[error("业务规则违反: {0}")]
    BusinessRuleViolation(String),
    
    #[error("外部依赖错误: {0}")]
    ExternalDependencyError(String),
    
    #[error("内部服务错误: {0}")]
    InternalError(String),
}

/// DataSource业务服务
/// 
/// 提供DataSource相关的核心业务逻辑，协调各个组件
pub struct DataSourceService {
    /// 数据源仓库
    datasource_repo: Arc<DataSourceRepository>,
    /// 验证服务
    validation_service: Arc<ValidationService>,
    /// 缓存服务
    cache_service: Arc<CacheService>,
}

impl DataSourceService {
    /// 创建新的DataSource业务服务实例
    /// 
    /// # 参数
    /// * `datasource_repo` - 数据源仓库
    /// * `validation_service` - 验证服务
    /// * `cache_service` - 缓存服务
    pub fn new(
        datasource_repo: Arc<DataSourceRepository>,
        validation_service: Arc<ValidationService>,
        cache_service: Arc<CacheService>,
    ) -> Self {
        Self {
            datasource_repo,
            validation_service,
            cache_service,
        }
    }
    
    /// 创建新的数据源
    /// 
    /// # 参数
    /// * `request` - 创建数据源请求
    /// 
    /// # 返回
    /// 创建成功返回数据源ID
    pub async fn create_datasource(
        &self,
        request: CreateDataSourceRequest,
    ) -> Result<String, ServiceError> {
        info!("开始创建数据源: name={}", request.name);
        
        // 1. 验证请求数据
        let validation_result = self.validation_service.validate_create_request(&request);
        if !validation_result.is_valid {
            let error_messages: Vec<String> = validation_result
                .errors
                .iter()
                .map(|e| e.to_string())
                .collect();
            let error_msg = error_messages.join("; ");
            warn!("数据源创建验证失败: {}", error_msg);
            return Err(ServiceError::ValidationFailed(error_msg));
        }
        
        // 2. 验证连接器存在性
        if !self.validation_service.connector_exists(&request.connector.id) {
            let error_msg = format!("连接器不存在: {}", request.connector.id);
            warn!("{}", error_msg);
            return Err(ServiceError::ValidationFailed(error_msg));
        }
        
        // 3. 检查名称是否重复
        if let Err(e) = self.check_name_uniqueness(&request.name).await {
            warn!("数据源名称检查失败: {}", e);
            return Err(e);
        }
        
        // 4. 创建数据源
        let datasource = DataSource::from(request).with_timestamps();
        let id = self
            .datasource_repo
            .create(&datasource)
            .await
            .map_err(|e| ServiceError::InternalError(e.to_string()))?;
        
        // 5. 清理相关缓存
        if let Err(e) = self.cache_service.invalidate_datasource_caches().await {
            warn!("清理缓存失败: {}", e);
            // 缓存清理失败不影响主要业务流程
        }
        
        info!("数据源创建成功: id={}", id);
        Ok(id)
    }
    
    /// 根据ID获取数据源
    /// 
    /// # 参数
    /// * `id` - 数据源ID
    /// 
    /// # 返回
    /// 如果找到返回Some(DataSource)，否则返回None
    pub async fn get_datasource(&self, id: &str) -> Result<Option<DataSource>, ServiceError> {
        debug!("获取数据源: id={}", id);
        
        self.datasource_repo
            .get_by_id(id)
            .await
            .map_err(|e| ServiceError::InternalError(e.to_string()))
    }
    
    /// 更新数据源
    /// 
    /// # 参数
    /// * `id` - 数据源ID
    /// * `request` - 更新数据源请求
    pub async fn update_datasource(
        &self,
        id: &str,
        request: UpdateDataSourceRequest,
    ) -> Result<(), ServiceError> {
        info!("开始更新数据源: id={}", id);
        
        // 1. 验证请求数据
        let validation_result = self.validation_service.validate_update_request(&request);
        if !validation_result.is_valid {
            let error_messages: Vec<String> = validation_result
                .errors
                .iter()
                .map(|e| e.to_string())
                .collect();
            let error_msg = error_messages.join("; ");
            warn!("数据源更新验证失败: {}", error_msg);
            return Err(ServiceError::ValidationFailed(error_msg));
        }
        
        // 2. 获取现有数据源
        let mut existing_datasource = self
            .datasource_repo
            .get_by_id(id)
            .await
            .map_err(|e| ServiceError::InternalError(e.to_string()))?
            .ok_or_else(|| ServiceError::NotFound(id.to_string()))?;
        
        // 3. 应用更新
        self.apply_update_to_datasource(&mut existing_datasource, request).await?;
        
        // 4. 更新数据源
        self.datasource_repo
            .update(id, &existing_datasource)
            .await
            .map_err(|e| ServiceError::InternalError(e.to_string()))?;
        
        // 5. 清理相关缓存
        if let Err(e) = self.cache_service.invalidate_datasource_cache(id).await {
            warn!("清理缓存失败: {}", e);
        }
        
        info!("数据源更新成功: id={}", id);
        Ok(())
    }
    
    /// 删除数据源
    /// 
    /// # 参数
    /// * `id` - 数据源ID
    pub async fn delete_datasource(&self, id: &str) -> Result<(), ServiceError> {
        info!("开始删除数据源: id={}", id);
        
        // 1. 检查数据源是否存在
        let datasource = self
            .datasource_repo
            .get_by_id(id)
            .await
            .map_err(|e| ServiceError::InternalError(e.to_string()))?
            .ok_or_else(|| ServiceError::NotFound(id.to_string()))?;
        
        // 2. 检查业务规则（例如：是否有关联的文档）
        if let Err(e) = self.check_delete_constraints(id, &datasource).await {
            warn!("删除约束检查失败: {}", e);
            return Err(e);
        }
        
        // 3. 删除数据源
        self.datasource_repo
            .delete(id)
            .await
            .map_err(|e| ServiceError::InternalError(e.to_string()))?;
        
        // 4. 清理相关缓存
        if let Err(e) = self.cache_service.invalidate_datasource_cache(id).await {
            warn!("清理缓存失败: {}", e);
        }
        
        info!("数据源删除成功: id={}", id);
        Ok(())
    }
    
    /// 搜索数据源
    /// 
    /// # 参数
    /// * `query` - 搜索查询参数
    /// 
    /// # 返回
    /// 搜索结果
    pub async fn search_datasources(
        &self,
        query: SearchQuery,
    ) -> Result<SearchResponse, ServiceError> {
        debug!("搜索数据源: query={:?}", query);
        
        self.datasource_repo
            .search(&query)
            .await
            .map_err(|e| ServiceError::InternalError(e.to_string()))
    }
    
    /// 在数据源中创建文档
    /// 
    /// # 参数
    /// * `datasource_id` - 数据源ID
    /// * `request` - 创建文档请求
    /// * `doc_id` - 可选的文档ID
    /// 
    /// # 返回
    /// 创建成功返回文档ID
    pub async fn create_document(
        &self,
        datasource_id: &str,
        request: CreateDocumentRequest,
        doc_id: Option<String>,
    ) -> Result<String, ServiceError> {
        info!("在数据源中创建文档: datasource_id={}", datasource_id);
        
        // 1. 验证数据源存在
        let _datasource = self
            .datasource_repo
            .get_by_id(datasource_id)
            .await
            .map_err(|e| ServiceError::InternalError(e.to_string()))?
            .ok_or_else(|| ServiceError::NotFound(datasource_id.to_string()))?;
        
        // 2. 验证请求数据
        let validation_result = self.validation_service.validate_create_document_request(&request);
        if !validation_result.is_valid {
            let error_messages: Vec<String> = validation_result
                .errors
                .iter()
                .map(|e| e.to_string())
                .collect();
            let error_msg = error_messages.join("; ");
            warn!("文档创建验证失败: {}", error_msg);
            return Err(ServiceError::ValidationFailed(error_msg));
        }
        
        // 3. 创建文档
        let mut document = Document::from(request);
        document.datasource_id = datasource_id.to_string();
        document = document.with_timestamps();
        
        if let Some(id) = doc_id {
            document = document.with_id(id);
        }
        
        // TODO: 实现文档存储逻辑
        // 这里需要根据实际需求实现文档的存储
        
        let document_id = document.id.unwrap_or_else(|| uuid::Uuid::new_v4().to_string());
        
        info!("文档创建成功: document_id={}", document_id);
        Ok(document_id)
    }
    
    /// 检查名称唯一性
    async fn check_name_uniqueness(&self, name: &str) -> Result<(), ServiceError> {
        // TODO: 实现名称唯一性检查
        // 这里可以通过搜索来检查是否已存在同名的数据源
        debug!("检查数据源名称唯一性: name={}", name);
        Ok(())
    }
    
    /// 应用更新到数据源
    async fn apply_update_to_datasource(
        &self,
        datasource: &mut DataSource,
        request: UpdateDataSourceRequest,
    ) -> Result<(), ServiceError> {
        // 应用字段更新
        if let Some(name) = request.name {
            // 检查新名称的唯一性
            if name != datasource.name {
                self.check_name_uniqueness(&name).await?;
            }
            datasource.name = name;
        }
        
        if let Some(r#type) = request.r#type {
            datasource.r#type = r#type;
        }
        
        if let Some(description) = request.description {
            datasource.description = Some(description);
        }
        
        if let Some(category) = request.category {
            datasource.category = Some(category);
        }
        
        if let Some(tags) = request.tags {
            datasource.tags = Some(tags);
        }
        
        if let Some(connector) = request.connector {
            // 验证新连接器存在性
            if !self.validation_service.connector_exists(&connector.id) {
                return Err(ServiceError::ValidationFailed(format!(
                    "连接器不存在: {}",
                    connector.id
                )));
            }
            datasource.connector = Some(connector);
        }
        
        if let Some(sync_enabled) = request.sync_enabled {
            datasource.sync_enabled = sync_enabled;
        }
        
        if let Some(enabled) = request.enabled {
            datasource.enabled = enabled;
        }
        
        // 更新时间戳
        datasource.updated = Some(chrono::Utc::now());
        
        Ok(())
    }
    
    /// 检查删除约束
    async fn check_delete_constraints(
        &self,
        _id: &str,
        _datasource: &DataSource,
    ) -> Result<(), ServiceError> {
        // TODO: 实现删除约束检查
        // 例如：检查是否有关联的文档、是否被其他资源引用等
        debug!("检查数据源删除约束");
        Ok(())
    }
}

/// 错误类型转换
impl From<ServiceError> for CocoError {
    fn from(error: ServiceError) -> Self {
        match error {
            ServiceError::ValidationFailed(msg) => CocoError::InvalidRequest(msg),
            ServiceError::NotFound(msg) => CocoError::InvalidRequest(format!("资源不存在: {}", msg)),
            ServiceError::BusinessRuleViolation(msg) => CocoError::InvalidRequest(msg),
            ServiceError::ExternalDependencyError(msg) => CocoError::ServerError(msg),
            ServiceError::InternalError(msg) => CocoError::ServerError(msg),
        }
    }
}

impl From<ValidationError> for ServiceError {
    fn from(error: ValidationError) -> Self {
        ServiceError::ValidationFailed(error.to_string())
    }
}

impl From<RepositoryError> for ServiceError {
    fn from(error: RepositoryError) -> Self {
        match error {
            RepositoryError::NotFound(msg) => ServiceError::NotFound(msg),
            RepositoryError::ValidationError(msg) => ServiceError::ValidationFailed(msg),
            _ => ServiceError::InternalError(error.to_string()),
        }
    }
}
