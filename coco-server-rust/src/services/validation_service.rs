use crate::models::connector::ConnectorConfig;
use crate::models::datasource::*;
use crate::models::document::*;
use regex::Regex;
use std::collections::HashMap;
use thiserror::Error;

/// 验证错误类型
#[derive(Debug, Error)]
pub enum ValidationError {
    #[error("字段不能为空: {field}")]
    Required { field: String },

    #[error("字段长度无效: {field}, 期望: {min}-{max}, 实际: {actual}")]
    InvalidLength {
        field: String,
        min: usize,
        max: usize,
        actual: usize,
    },

    #[error("字段格式无效: {field}, 原因: {reason}")]
    InvalidFormat { field: String, reason: String },

    #[error("字段值无效: {field}, 值: {value}, 原因: {reason}")]
    InvalidValue {
        field: String,
        value: String,
        reason: String,
    },

    #[error("连接器不存在: {connector_id}")]
    ConnectorNotFound { connector_id: String },
}

/// 验证结果
#[derive(Debug)]
pub struct ValidationResult {
    /// 是否验证通过
    pub is_valid: bool,
    /// 错误列表
    pub errors: Vec<ValidationError>,
}

impl ValidationResult {
    /// 创建成功的验证结果
    pub fn success() -> Self {
        Self {
            is_valid: true,
            errors: Vec::new(),
        }
    }

    /// 创建失败的验证结果
    pub fn failure(errors: Vec<ValidationError>) -> Self {
        Self {
            is_valid: false,
            errors,
        }
    }

    /// 添加错误
    pub fn add_error(&mut self, error: ValidationError) {
        self.errors.push(error);
        self.is_valid = false;
    }
}

/// 数据验证服务
///
/// 提供各种数据验证功能，确保输入数据的正确性和完整性
pub struct ValidationService {
    /// 支持的连接器ID列表
    supported_connectors: HashMap<String, ConnectorInfo>,
}

/// 连接器信息
#[derive(Debug, Clone)]
struct ConnectorInfo {
    /// 连接器ID
    id: String,
    /// 连接器名称
    name: String,
    /// 是否启用
    enabled: bool,
}

impl ValidationService {
    /// 创建新的验证服务实例
    pub fn new() -> Self {
        let mut supported_connectors = HashMap::new();

        // 添加支持的连接器
        supported_connectors.insert(
            "hugo_site".to_string(),
            ConnectorInfo {
                id: "hugo_site".to_string(),
                name: "Hugo Site".to_string(),
                enabled: true,
            },
        );

        supported_connectors.insert(
            "file_system".to_string(),
            ConnectorInfo {
                id: "file_system".to_string(),
                name: "File System".to_string(),
                enabled: true,
            },
        );

        supported_connectors.insert(
            "database".to_string(),
            ConnectorInfo {
                id: "database".to_string(),
                name: "Database".to_string(),
                enabled: true,
            },
        );

        Self {
            supported_connectors,
        }
    }

    /// 验证创建数据源请求
    ///
    /// # 参数
    /// * `request` - 创建数据源请求
    ///
    /// # 返回
    /// 验证结果
    pub fn validate_create_request(&self, request: &CreateDataSourceRequest) -> ValidationResult {
        let mut result = ValidationResult::success();

        // 验证名称
        self.validate_name(&request.name, &mut result);

        // 验证类型
        self.validate_type(&request.r#type, &mut result);

        // 验证描述
        if let Some(description) = &request.description {
            self.validate_description(description, &mut result);
        }

        // 验证分类
        if let Some(category) = &request.category {
            self.validate_category(category, &mut result);
        }

        // 验证标签
        if let Some(tags) = &request.tags {
            self.validate_tags(tags, &mut result);
        }

        // 验证连接器
        self.validate_connector(&request.connector, &mut result);

        result
    }

    /// 验证更新数据源请求
    ///
    /// # 参数
    /// * `request` - 更新数据源请求
    ///
    /// # 返回
    /// 验证结果
    pub fn validate_update_request(&self, request: &UpdateDataSourceRequest) -> ValidationResult {
        let mut result = ValidationResult::success();

        // 验证名称（如果提供）
        if let Some(name) = &request.name {
            self.validate_name(name, &mut result);
        }

        // 验证类型（如果提供）
        if let Some(r#type) = &request.r#type {
            self.validate_type(r#type, &mut result);
        }

        // 验证描述（如果提供）
        if let Some(description) = &request.description {
            self.validate_description(description, &mut result);
        }

        // 验证分类（如果提供）
        if let Some(category) = &request.category {
            self.validate_category(category, &mut result);
        }

        // 验证标签（如果提供）
        if let Some(tags) = &request.tags {
            self.validate_tags(tags, &mut result);
        }

        // 验证连接器（如果提供）
        if let Some(connector) = &request.connector {
            self.validate_connector(connector, &mut result);
        }

        result
    }

    /// 验证创建文档请求
    ///
    /// # 参数
    /// * `request` - 创建文档请求
    ///
    /// # 返回
    /// 验证结果
    pub fn validate_create_document_request(
        &self,
        request: &CreateDocumentRequest,
    ) -> ValidationResult {
        let mut result = ValidationResult::success();

        // 验证标题（如果提供）
        if let Some(title) = &request.title {
            if title.len() > 500 {
                result.add_error(ValidationError::InvalidLength {
                    field: "title".to_string(),
                    min: 0,
                    max: 500,
                    actual: title.len(),
                });
            }
        }

        // 验证内容（如果提供）
        if let Some(content) = &request.content {
            if content.len() > 1_000_000 {
                result.add_error(ValidationError::InvalidLength {
                    field: "content".to_string(),
                    min: 0,
                    max: 1_000_000,
                    actual: content.len(),
                });
            }
        }

        result
    }

    /// 验证名称
    fn validate_name(&self, name: &str, result: &mut ValidationResult) {
        if name.is_empty() {
            result.add_error(ValidationError::Required {
                field: "name".to_string(),
            });
            return;
        }

        if name.len() < 2 || name.len() > 100 {
            result.add_error(ValidationError::InvalidLength {
                field: "name".to_string(),
                min: 2,
                max: 100,
                actual: name.len(),
            });
        }

        // 检查名称格式（允许字母、数字、空格、连字符、下划线）
        let name_regex = Regex::new(r"^[a-zA-Z0-9\s\-_\u4e00-\u9fff]+$").unwrap();
        if !name_regex.is_match(name) {
            result.add_error(ValidationError::InvalidFormat {
                field: "name".to_string(),
                reason: "名称只能包含字母、数字、空格、连字符、下划线和中文字符".to_string(),
            });
        }
    }

    /// 验证类型
    fn validate_type(&self, r#type: &str, result: &mut ValidationResult) {
        if r#type.is_empty() {
            result.add_error(ValidationError::Required {
                field: "type".to_string(),
            });
            return;
        }

        // 目前只支持 "connector" 类型
        if r#type != "connector" {
            result.add_error(ValidationError::InvalidValue {
                field: "type".to_string(),
                value: r#type.to_string(),
                reason: "目前只支持 'connector' 类型".to_string(),
            });
        }
    }

    /// 验证描述
    fn validate_description(&self, description: &str, result: &mut ValidationResult) {
        if description.len() > 1000 {
            result.add_error(ValidationError::InvalidLength {
                field: "description".to_string(),
                min: 0,
                max: 1000,
                actual: description.len(),
            });
        }
    }

    /// 验证分类
    fn validate_category(&self, category: &str, result: &mut ValidationResult) {
        if category.len() > 50 {
            result.add_error(ValidationError::InvalidLength {
                field: "category".to_string(),
                min: 0,
                max: 50,
                actual: category.len(),
            });
        }

        // 检查分类格式（只允许字母、数字、下划线）
        let category_regex = Regex::new(r"^[a-zA-Z0-9_]+$").unwrap();
        if !category_regex.is_match(category) {
            result.add_error(ValidationError::InvalidFormat {
                field: "category".to_string(),
                reason: "分类只能包含字母、数字和下划线".to_string(),
            });
        }
    }

    /// 验证标签
    fn validate_tags(&self, tags: &[String], result: &mut ValidationResult) {
        if tags.len() > 20 {
            result.add_error(ValidationError::InvalidValue {
                field: "tags".to_string(),
                value: tags.len().to_string(),
                reason: "标签数量不能超过20个".to_string(),
            });
        }

        for (i, tag) in tags.iter().enumerate() {
            if tag.is_empty() {
                result.add_error(ValidationError::InvalidValue {
                    field: format!("tags[{}]", i),
                    value: tag.clone(),
                    reason: "标签不能为空".to_string(),
                });
            } else if tag.len() > 30 {
                result.add_error(ValidationError::InvalidLength {
                    field: format!("tags[{}]", i),
                    min: 1,
                    max: 30,
                    actual: tag.len(),
                });
            }
        }
    }

    /// 验证连接器
    fn validate_connector(&self, connector: &ConnectorConfig, result: &mut ValidationResult) {
        if connector.id.is_empty() {
            result.add_error(ValidationError::Required {
                field: "connector.id".to_string(),
            });
            return;
        }

        // 检查连接器是否支持
        if !self.supported_connectors.contains_key(&connector.id) {
            result.add_error(ValidationError::ConnectorNotFound {
                connector_id: connector.id.clone(),
            });
        }
    }

    /// 检查连接器是否存在
    ///
    /// # 参数
    /// * `connector_id` - 连接器ID
    ///
    /// # 返回
    /// 如果连接器存在返回true，否则返回false
    pub fn connector_exists(&self, connector_id: &str) -> bool {
        self.supported_connectors.contains_key(connector_id)
    }

    /// 获取支持的连接器列表
    pub fn get_supported_connectors(&self) -> Vec<String> {
        self.supported_connectors.keys().cloned().collect()
    }
}

impl Default for ValidationService {
    fn default() -> Self {
        Self::new()
    }
}
