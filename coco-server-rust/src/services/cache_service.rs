use crate::repositories::cache_manager::{cache_keys, CacheManager};
use std::sync::Arc;
use tracing::{debug, info, warn};

/// 缓存服务
/// 
/// 提供业务层的缓存管理功能，包括缓存策略和失效管理
pub struct CacheService {
    /// 缓存管理器
    cache_manager: Arc<CacheManager>,
}

impl CacheService {
    /// 创建新的缓存服务实例
    /// 
    /// # 参数
    /// * `cache_manager` - 缓存管理器
    pub fn new(cache_manager: Arc<CacheManager>) -> Self {
        Self { cache_manager }
    }
    
    /// 预热数据源缓存
    /// 
    /// 在系统启动时预热热点数据，提高响应速度
    pub async fn warmup_datasource_cache(&self) -> Result<(), String> {
        info!("开始预热数据源缓存");
        
        // TODO: 实现缓存预热逻辑
        // 1. 从数据库加载热点数据源
        // 2. 将数据源信息缓存到内存
        // 3. 缓存启用/禁用的数据源ID列表
        
        info!("数据源缓存预热完成");
        Ok(())
    }
    
    /// 失效所有数据源相关缓存
    /// 
    /// 当数据源发生变更时，清理所有相关缓存
    pub async fn invalidate_datasource_caches(&self) -> Result<(), String> {
        debug!("开始失效数据源相关缓存");
        
        // 失效列表缓存
        self.cache_manager.remove(cache_keys::ENABLED_DATASOURCE_IDS);
        self.cache_manager.remove(cache_keys::DISABLED_DATASOURCE_IDS);
        
        // TODO: 失效所有数据源项目缓存
        // 这里可以通过模式匹配来删除所有以特定前缀开头的缓存键
        
        debug!("数据源相关缓存失效完成");
        Ok(())
    }
    
    /// 失效特定数据源的缓存
    /// 
    /// # 参数
    /// * `datasource_id` - 数据源ID
    pub async fn invalidate_datasource_cache(&self, datasource_id: &str) -> Result<(), String> {
        debug!("失效数据源缓存: id={}", datasource_id);
        
        // 失效主缓存
        let primary_key = cache_keys::build_key(cache_keys::DATASOURCE_PRIMARY_PREFIX, datasource_id);
        self.cache_manager.remove(&primary_key);
        
        // 失效项目缓存
        let items_key = cache_keys::build_key(cache_keys::DATASOURCE_ITEMS_PREFIX, datasource_id);
        self.cache_manager.remove(&items_key);
        
        // 失效列表缓存
        self.cache_manager.remove(cache_keys::ENABLED_DATASOURCE_IDS);
        self.cache_manager.remove(cache_keys::DISABLED_DATASOURCE_IDS);
        
        debug!("数据源缓存失效完成: id={}", datasource_id);
        Ok(())
    }
    
    /// 缓存启用的数据源ID列表
    /// 
    /// # 参数
    /// * `datasource_ids` - 数据源ID列表
    /// * `ttl` - 缓存TTL（秒）
    pub async fn cache_enabled_datasource_ids(
        &self,
        datasource_ids: &[String],
        ttl: Option<i64>,
    ) -> Result<(), String> {
        let ids_json = serde_json::to_string(datasource_ids)
            .map_err(|e| format!("序列化启用数据源ID列表失败: {}", e))?;
        
        self.cache_manager
            .set(
                cache_keys::ENABLED_DATASOURCE_IDS.to_string(),
                ids_json,
                ttl,
            )
            .map_err(|e| format!("缓存启用数据源ID列表失败: {}", e))?;
        
        debug!("已缓存启用的数据源ID列表，数量: {}", datasource_ids.len());
        Ok(())
    }
    
    /// 获取缓存的启用数据源ID列表
    /// 
    /// # 返回
    /// 如果缓存存在返回Some(Vec<String>)，否则返回None
    pub async fn get_cached_enabled_datasource_ids(&self) -> Result<Option<Vec<String>>, String> {
        if let Some(cached_json) = self.cache_manager.get(cache_keys::ENABLED_DATASOURCE_IDS) {
            match serde_json::from_str::<Vec<String>>(&cached_json) {
                Ok(ids) => {
                    debug!("从缓存获取启用数据源ID列表，数量: {}", ids.len());
                    Ok(Some(ids))
                }
                Err(e) => {
                    warn!("反序列化启用数据源ID列表失败: {}, 删除缓存", e);
                    self.cache_manager.remove(cache_keys::ENABLED_DATASOURCE_IDS);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// 缓存禁用的数据源ID列表
    /// 
    /// # 参数
    /// * `datasource_ids` - 数据源ID列表
    /// * `ttl` - 缓存TTL（秒）
    pub async fn cache_disabled_datasource_ids(
        &self,
        datasource_ids: &[String],
        ttl: Option<i64>,
    ) -> Result<(), String> {
        let ids_json = serde_json::to_string(datasource_ids)
            .map_err(|e| format!("序列化禁用数据源ID列表失败: {}", e))?;
        
        self.cache_manager
            .set(
                cache_keys::DISABLED_DATASOURCE_IDS.to_string(),
                ids_json,
                ttl,
            )
            .map_err(|e| format!("缓存禁用数据源ID列表失败: {}", e))?;
        
        debug!("已缓存禁用的数据源ID列表，数量: {}", datasource_ids.len());
        Ok(())
    }
    
    /// 获取缓存的禁用数据源ID列表
    /// 
    /// # 返回
    /// 如果缓存存在返回Some(Vec<String>)，否则返回None
    pub async fn get_cached_disabled_datasource_ids(&self) -> Result<Option<Vec<String>>, String> {
        if let Some(cached_json) = self.cache_manager.get(cache_keys::DISABLED_DATASOURCE_IDS) {
            match serde_json::from_str::<Vec<String>>(&cached_json) {
                Ok(ids) => {
                    debug!("从缓存获取禁用数据源ID列表，数量: {}", ids.len());
                    Ok(Some(ids))
                }
                Err(e) => {
                    warn!("反序列化禁用数据源ID列表失败: {}, 删除缓存", e);
                    self.cache_manager.remove(cache_keys::DISABLED_DATASOURCE_IDS);
                    Ok(None)
                }
            }
        } else {
            Ok(None)
        }
    }
    
    /// 获取缓存统计信息
    pub fn get_cache_stats(&self) -> serde_json::Value {
        let stats = self.cache_manager.stats();
        serde_json::json!({
            "total_items": stats.total_items,
            "hits": stats.hits,
            "misses": stats.misses,
            "hit_rate": stats.hit_rate
        })
    }
    
    /// 清理过期缓存
    pub fn cleanup_expired_cache(&self) {
        self.cache_manager.cleanup_expired();
    }
    
    /// 清空所有缓存
    pub fn clear_all_cache(&self) {
        self.cache_manager.clear();
        info!("已清空所有缓存");
    }
    
    /// 检查缓存项是否存在
    /// 
    /// # 参数
    /// * `key` - 缓存键
    pub fn cache_exists(&self, key: &str) -> bool {
        self.cache_manager.exists(key)
    }
    
    /// 设置自定义缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    /// * `value` - 缓存值
    /// * `ttl` - TTL（秒）
    pub fn set_cache(&self, key: String, value: String, ttl: Option<i64>) -> Result<(), String> {
        self.cache_manager.set(key, value, ttl)
    }
    
    /// 获取自定义缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    pub fn get_cache(&self, key: &str) -> Option<String> {
        self.cache_manager.get(key)
    }
    
    /// 删除自定义缓存项
    /// 
    /// # 参数
    /// * `key` - 缓存键
    pub fn remove_cache(&self, key: &str) -> bool {
        self.cache_manager.remove(key)
    }
}
