use axum::{
    body::Body,
    http::{Request, StatusCode},
    routing::{get, post},
    Router,
};
use std::sync::Arc;
use tower::ServiceExt;
use tower_http::cors::CorsLayer;

// 模拟配置管理器
struct ConfigManager;

impl ConfigManager {
    fn new() -> Result<Self, Box<dyn std::error::Error>> {
        Ok(ConfigManager)
    }
    
    fn get_port(&self) -> u16 {
        8080
    }
}

// 模拟处理器函数
async fn info_handler() -> Result<impl axum::response::IntoResponse, (StatusCode, String)> {
    Ok((StatusCode::OK, "Info endpoint"))
}

async fn initialize_handler() -> Result<impl axum::response::IntoResponse, (StatusCode, String)> {
    Ok((StatusCode::OK, "Initialize endpoint"))
}

async fn websocket_handler(
    ws: axum::extract::ws::WebSocketUpgrade,
) -> axum::response::Response {
    ws.on_upgrade(|_socket| async {})
}

#[tokio::test]
async fn test_info_endpoint() {
    // 创建应用路由
    let app = Router::new()
        .route("/_info", get(info_handler));

    // 创建一个请求
    let request = Request::builder()
        .uri("/_info")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_setup_initialize_endpoint() {
    // 创建应用路由
    let app = Router::new()
        .route("/setup/_initialize", post(initialize_handler));

    // 创建一个POST请求
    let request = Request::builder()
        .uri("/setup/_initialize")
        .method("POST")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_websocket_endpoint() {
    // 创建应用路由，包含CORS层
    let app = Router::new()
        .route("/ws", axum::routing::get(websocket_handler))
        .layer(CorsLayer::very_permissive());

    // 创建一个WebSocket升级请求
    let request = Request::builder()
        .uri("/ws")
        .header("connection", "upgrade")
        .header("upgrade", "websocket")
        .header("sec-websocket-version", "13")
        .header("sec-websocket-key", "dGhlIHNhbXBsZSBub25jZQ==")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码（WebSocket握手应该返回101 Switching Protocols）
    // 但由于测试环境限制，我们验证它不是404
    assert_ne!(response.status(), StatusCode::NOT_FOUND);
}

#[tokio::test]
async fn test_cors_headers() {
    // 创建应用路由，包含CORS层
    let app = Router::new()
        .route("/test", get(|| async { "test" }))
        .layer(CorsLayer::very_permissive());

    // 创建一个带Origin头部的请求（模拟跨域请求）
    let request = Request::builder()
        .uri("/test")
        .header("origin", "http://localhost:3000")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);
    
    // 验证CORS头部是否存在
    assert!(response.headers().contains_key("access-control-allow-origin"));
}