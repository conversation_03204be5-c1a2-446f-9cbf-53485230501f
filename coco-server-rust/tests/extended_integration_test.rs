use axum::{
    body::Body,
    http::{Request, StatusCode},
    routing::{get, post},
    Router,
};
use tower::ServiceExt;
use tower_http::cors::CorsLayer;

#[tokio::test]
async fn test_info_endpoint_integration() {
    // 创建应用路由
    let app = Router::new()
        .route("/_info", get(|| async { "Info endpoint" }));

    // 创建一个请求
    let request = Request::builder()
        .uri("/_info")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_setup_initialize_endpoint_integration() {
    // 创建应用路由
    let app = Router::new()
        .route("/setup/_initialize", post(|| async { "Initialize endpoint" }));

    // 创建一个POST请求
    let request = Request::builder()
        .uri("/setup/_initialize")
        .method("POST")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);
}

#[tokio::test]
async fn test_websocket_endpoint_integration() {
    // 创建一个简单的WebSocket处理函数
    async fn websocket_handler(
        ws: axum::extract::ws::WebSocketUpgrade,
    ) -> axum::response::Response {
        ws.on_upgrade(|_socket| async {})
    }
    
    // 创建应用路由，包含CORS层
    let app = Router::new()
        .route("/ws", axum::routing::get(websocket_handler))
        .layer(CorsLayer::very_permissive());

    // 创建一个WebSocket升级请求
    let request = Request::builder()
        .uri("/ws")
        .header("connection", "upgrade")
        .header("upgrade", "websocket")
        .header("sec-websocket-version", "13")
        .header("sec-websocket-key", "dGhlIHNhbXBsZSBub25jZQ==")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码（WebSocket握手应该返回101 Switching Protocols）
    // 但由于测试环境限制，我们验证它不是404
    assert_ne!(response.status(), StatusCode::NOT_FOUND);
}

#[tokio::test]
async fn test_cors_headers_integration() {
    // 创建应用路由，包含CORS层
    let app = Router::new()
        .route("/test", get(|| async { "test" }))
        .layer(CorsLayer::very_permissive());

    // 创建一个带Origin头部的请求（模拟跨域请求）
    let request = Request::builder()
        .uri("/test")
        .header("origin", "http://localhost:3000")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::OK);
    
    // 验证CORS头部是否存在
    assert!(response.headers().contains_key("access-control-allow-origin"));
}

#[tokio::test]
async fn test_error_handling_integration() {
    // 创建应用路由
    let app = Router::new()
        .route("/error", get(|| async { 
            (StatusCode::INTERNAL_SERVER_ERROR, "Internal server error")
        }));

    // 创建一个请求
    let request = Request::builder()
        .uri("/error")
        .body(Body::empty())
        .unwrap();

    // 发送请求
    let response = app.oneshot(request).await.unwrap();
    
    // 验证响应状态码
    assert_eq!(response.status(), StatusCode::INTERNAL_SERVER_ERROR);
}