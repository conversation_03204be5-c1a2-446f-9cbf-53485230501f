# SurrealDB配置指南

## 1. SurrealDB架构概述

### 1.1 为什么选择SurrealDB
- **统一架构**: 替代Elasticsearch，实现单一数据库架构
- **实时能力**: 内置Live Query支持实时数据推送
- **全文搜索**: 内置BM25算法和高亮显示
- **类型安全**: 强类型系统保证数据一致性
- **关系处理**: 原生支持图数据库关系

### 1.2 架构对比

| 功能 | Elasticsearch | SurrealDB |
|------|---------------|-----------|
| 全文搜索 | ✅ 专业搜索引擎 | ✅ 内置BM25算法 |
| 拼音搜索 | ✅ 插件支持 | ⚠️ 自定义分析器 |
| 实时查询 | ❌ 需要轮询 | ✅ Live Query |
| 关系查询 | ❌ 文档数据库 | ✅ 图数据库 |
| 事务支持 | ❌ 最终一致性 | ✅ ACID事务 |
| 运维复杂度 | 🔴 高 | 🟢 低 |

## 2. SurrealDB配置

### 2.1 基础配置

```toml
# Cargo.toml
[dependencies]
surrealdb = { version = "1.0", features = ["kv-rocksdb", "scripting"] }
tokio = { version = "1.0", features = ["full"] }
serde = { version = "1.0", features = ["derive"] }
```

### 2.2 连接配置

```rust
// src/config/database.rs
use surrealdb::{Surreal, engine::remote::ws::Ws};
use surrealdb::opt::auth::Root;

pub struct DatabaseConfig {
    pub url: String,
    pub namespace: String,
    pub database: String,
    pub username: String,
    pub password: String,
}

impl DatabaseConfig {
    pub async fn connect(&self) -> Result<Surreal<Ws>, surrealdb::Error> {
        let db = Surreal::new::<Ws>(&self.url).await?;
        
        // 认证
        db.signin(Root {
            username: &self.username,
            password: &self.password,
        }).await?;
        
        // 选择命名空间和数据库
        db.use_ns(&self.namespace).use_db(&self.database).await?;
        
        Ok(db)
    }
}
```

## 3. 全文搜索配置

### 3.1 分析器定义

```sql
-- 定义基础分析器
DEFINE ANALYZER basic_analyzer 
    TOKENIZERS blank, punct, camel, class
    FILTERS lowercase, ascii;

-- 定义中文分析器
DEFINE ANALYZER chinese_analyzer 
    TOKENIZERS blank, punct, class
    FILTERS lowercase, ascii, ngram(2,3);

-- 定义拼音分析器（需要自定义mapper）
DEFINE ANALYZER pinyin_analyzer 
    TOKENIZERS blank, punct, class
    FILTERS lowercase, ascii, mapper('pinyin_mapping.txt');
```

### 3.2 搜索索引配置

```sql
-- DataSource表定义
DEFINE TABLE datasource SCHEMAFULL;

-- 字段定义
DEFINE FIELD id ON TABLE datasource TYPE string;
DEFINE FIELD name ON TABLE datasource TYPE string;
DEFINE FIELD description ON TABLE datasource TYPE option<string>;
DEFINE FIELD connector_config ON TABLE datasource TYPE object;
DEFINE FIELD created_at ON TABLE datasource TYPE datetime DEFAULT time::now();
DEFINE FIELD updated_at ON TABLE datasource TYPE datetime DEFAULT time::now();

-- 搜索索引
DEFINE INDEX datasource_search ON TABLE datasource 
    FIELDS name, description 
    SEARCH ANALYZER basic_analyzer BM25 HIGHLIGHTS;

-- 拼音搜索索引
DEFINE INDEX datasource_pinyin ON TABLE datasource 
    FIELDS name, description 
    SEARCH ANALYZER pinyin_analyzer BM25;
```

## 4. 实时查询配置

### 4.1 Live Query示例

```rust
// src/realtime/live_query.rs
use surrealdb::{Surreal, engine::remote::ws::Ws};
use tokio_stream::StreamExt;

pub struct LiveQueryManager {
    db: Surreal<Ws>,
}

impl LiveQueryManager {
    pub async fn watch_datasources(&self) -> Result<(), surrealdb::Error> {
        let mut stream = self.db
            .query("LIVE SELECT * FROM datasource")
            .await?
            .stream::<serde_json::Value>(0)?;

        while let Some(result) = stream.next().await {
            match result {
                Ok(notification) => {
                    println!("数据变更: {:?}", notification);
                    // 处理实时数据变更
                }
                Err(e) => {
                    eprintln!("Live Query错误: {}", e);
                }
            }
        }

        Ok(())
    }
}
```

## 5. 搜索功能实现

### 5.1 基础搜索

```rust
// src/search/search_service.rs
use surrealdb::{Surreal, engine::remote::ws::Ws};

pub struct SearchService {
    db: Surreal<Ws>,
}

impl SearchService {
    pub async fn search_datasources(
        &self, 
        query: &str, 
        limit: usize
    ) -> Result<Vec<DataSource>, surrealdb::Error> {
        let sql = format!(
            "SELECT *, search::score(1) AS score, search::highlight('<b>', '</b>', 1) AS highlight
             FROM datasource 
             WHERE name @1@ $query OR description @1@ $query
             ORDER BY score DESC
             LIMIT {}", 
            limit
        );

        let mut result = self.db
            .query(sql)
            .bind(("query", query))
            .await?;

        let datasources: Vec<DataSource> = result.take(0)?;
        Ok(datasources)
    }
}
```

### 5.2 拼音搜索实现

```rust
// src/search/pinyin_search.rs
pub struct PinyinSearchService {
    db: Surreal<Ws>,
}

impl PinyinSearchService {
    pub async fn setup_pinyin_mapping(&self) -> Result<(), surrealdb::Error> {
        // 创建拼音映射表
        self.db.query("
            CREATE pinyin_mapping SET
                char = '中',
                pinyin = ['zhong', 'zhōng', 'zhòng']
        ").await?;

        Ok(())
    }

    pub async fn search_with_pinyin(
        &self, 
        query: &str
    ) -> Result<Vec<DataSource>, surrealdb::Error> {
        // 实现拼音搜索逻辑
        let sql = "
            SELECT *, search::score(2) AS score
            FROM datasource 
            WHERE name @2@ $query OR description @2@ $query
            ORDER BY score DESC
        ";

        let mut result = self.db
            .query(sql)
            .bind(("query", query))
            .await?;

        let datasources: Vec<DataSource> = result.take(0)?;
        Ok(datasources)
    }
}
```

## 6. 性能优化

### 6.1 索引优化

```sql
-- 复合索引
DEFINE INDEX datasource_compound ON TABLE datasource 
    FIELDS name, created_at;

-- 唯一索引
DEFINE INDEX datasource_unique_name ON TABLE datasource 
    FIELDS name UNIQUE;
```

### 6.2 查询优化

```rust
// 批量操作优化
pub async fn batch_create_datasources(
    &self, 
    datasources: Vec<CreateDataSourceRequest>
) -> Result<Vec<DataSource>, surrealdb::Error> {
    let mut tx = self.db.begin().await?;
    
    let mut results = Vec::new();
    for ds in datasources {
        let result: DataSource = tx
            .create("datasource")
            .content(ds)
            .await?;
        results.push(result);
    }
    
    tx.commit().await?;
    Ok(results)
}
```

## 7. 迁移策略

### 7.1 数据迁移

```rust
// src/migration/elasticsearch_to_surrealdb.rs
pub struct DataMigration {
    es_client: elasticsearch::Elasticsearch,
    surreal_db: Surreal<Ws>,
}

impl DataMigration {
    pub async fn migrate_datasources(&self) -> Result<(), Box<dyn std::error::Error>> {
        // 从ES读取数据
        let es_data = self.fetch_from_elasticsearch().await?;
        
        // 转换数据格式
        let surreal_data = self.convert_data_format(es_data)?;
        
        // 写入SurrealDB
        self.write_to_surrealdb(surreal_data).await?;
        
        Ok(())
    }
}
```

### 7.2 功能对比验证

```rust
// src/validation/feature_comparison.rs
pub struct FeatureValidator {
    es_client: elasticsearch::Elasticsearch,
    surreal_db: Surreal<Ws>,
}

impl FeatureValidator {
    pub async fn validate_search_parity(&self, query: &str) -> Result<bool, Box<dyn std::error::Error>> {
        let es_results = self.search_elasticsearch(query).await?;
        let surreal_results = self.search_surrealdb(query).await?;
        
        // 比较搜索结果的相似度
        let similarity = self.calculate_similarity(&es_results, &surreal_results);
        
        Ok(similarity > 0.95) // 95%相似度阈值
    }
}
```

## 8. 监控和调试

### 8.1 性能监控

```rust
// src/monitoring/performance.rs
pub struct PerformanceMonitor {
    db: Surreal<Ws>,
}

impl PerformanceMonitor {
    pub async fn collect_metrics(&self) -> Result<DatabaseMetrics, surrealdb::Error> {
        let info = self.db.query("INFO FOR DB").await?;
        
        // 收集性能指标
        Ok(DatabaseMetrics {
            query_count: self.get_query_count().await?,
            avg_response_time: self.get_avg_response_time().await?,
            cache_hit_rate: self.get_cache_hit_rate().await?,
        })
    }
}
```

### 8.2 调试工具

```rust
// src/debug/query_analyzer.rs
pub struct QueryAnalyzer {
    db: Surreal<Ws>,
}

impl QueryAnalyzer {
    pub async fn explain_query(&self, sql: &str) -> Result<QueryPlan, surrealdb::Error> {
        let plan = self.db
            .query(format!("EXPLAIN {}", sql))
            .await?;
            
        // 分析查询计划
        Ok(QueryPlan::from(plan))
    }
}
```

---

这个配置指南提供了完整的SurrealDB迁移方案，确保功能对等性的同时，充分利用SurrealDB的优势特性。
