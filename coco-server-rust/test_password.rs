use bcrypt::{hash, verify, DEFAULT_COST};

fn main() {
    let password = "coco123";
    let saved_hash = "$2b$12$xkCgGGHspHC4aGavazr04OakNxGrTPIwW5ZzuObArYU0nIDUo/Ug.";

    println!("Testing password: {}", password);
    println!("Saved hash: {}", saved_hash);

    // 测试验证
    match verify(password, saved_hash) {
        Ok(is_valid) => {
            println!("Password verification result: {}", is_valid);
        }
        Err(e) => {
            println!("Password verification error: {}", e);
        }
    }

    // 生成新的哈希进行对比
    match hash(password, DEFAULT_COST) {
        Ok(new_hash) => {
            println!("New hash for comparison: {}", new_hash);

            // 验证新哈希
            match verify(password, &new_hash) {
                Ok(is_valid) => {
                    println!("New hash verification result: {}", is_valid);
                }
                Err(e) => {
                    println!("New hash verification error: {}", e);
                }
            }
        }
        Err(e) => {
            println!("Hash generation error: {}", e);
        }
    }
}
