---
weight: 20
title: "Setup Wizard"
---


## Setup Wizard

Open your browser and enter http://localhost:9000/#/guide to access the initialization wizard, as shown below:
{{% load-img "/img/setup-step1.png" "Initialization" %}}

Enter your username, email, and password, then click Next, as shown:
{{% load-img "/img/setup-step2.png" "Initialization" %}}

- Select the LLM type: DeepSeek, Ollama, or OpenAI
- Configure the LLM endpoint
- Set the default model
- Enable keepalive and set an appropriate interval
- Provide the token
- Click Next to complete the initialization.

### Login
After initialization, you’ll be redirected to the login page, as shown:
{{% load-img "/img/login.png" "Login" %}}

Enter your password and click Login. Upon successful login, you’ll be directed to the homepage:
{{% load-img "/img/home.png" "Home" %}}

### Settings
Click `Settings` in the left-side menu, as shown:
{{% load-img "/img/settings.png" "Settings" %}}

- Configure LLM parameters
- Configure Collector parameters

### Datasource
Click `Datasource` in the left-side menu:
{{% load-img "/img/datasource-list.png" "Datasource" %}}

To add a new datasource, click `Add` button, as shown:
{{% load-img "/img/new-datasource.png" "Add Datasource" %}}

Click on a `datasource name` in the list to view its details:
{{% load-img "/img/datasource-detail.png" "Datasource Detail" %}}
Enjoy~
