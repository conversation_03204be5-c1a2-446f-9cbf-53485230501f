{"permissions": {"allow": ["mcp__code-index-mcp__set_project_path", "mcp__deepwiki__read_wiki_structure", "mcp__vibedev-specs-mcp__vibedev_specs_workflow_start", "mcp__deep<PERSON><PERSON>__ask_question", "mcp__vibedev-specs-mcp__vibedev_specs_requirements_start", "mcp__vibedev-specs-mcp__vibedev_specs_requirements_confirmed", "mcp__vibedev-specs-mcp__vibedev_specs_design_start", "mcp__vibedev-specs-mcp__vibedev_specs_design_confirmed", "mcp__vibedev-specs-mcp__vibedev_specs_tasks_confirmed", "mcp__vibedev-specs-mcp__vibedev_specs_execute_start", "Bash(cargo build:*)", "Bash(ls:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(cargo run:*)", "<PERSON><PERSON>(curl:*)", "Bash(timeout 30 cargo run)", "Bash(cargo test:*)", "mcp__code-index-mcp__find_files", "Bash(grep:*)", "WebFetch(domain:deepwiki.com)", "mcp__vibedev-specs-mcp__vibedev_specs_tasks_start", "Bash(cargo check:*)", "Bash(npm run build:*)", "Bash(npm test)", "Bash(npm run:*)"], "deny": [], "additionalDirectories": ["/Users/<USER>/local_doc/l_dev/my/rust/aigui/coco-app"]}}