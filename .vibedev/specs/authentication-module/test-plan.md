# 认证模块测试计划

## 测试策略概览

本测试计划采用多层次测试策略，确保认证模块的功能正确性、性能达标和与Go版本的兼容性。

### 测试层次
1. **单元测试** - 测试单个函数和模块
2. **集成测试** - 测试模块间交互
3. **API测试** - 测试HTTP端点
4. **兼容性测试** - 验证与Go版本兼容
5. **性能测试** - 验证性能指标
6. **安全测试** - 验证安全性

## 单元测试计划

### 密码管理器测试 (password_manager.rs)
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_verify_password_success() {
        // 测试正确密码验证
    }

    #[tokio::test]
    async fn test_verify_password_failure() {
        // 测试错误密码验证
    }

    #[tokio::test]
    async fn test_hash_password() {
        // 测试密码哈希生成
    }

    #[tokio::test]
    async fn test_bcrypt_cost_configuration() {
        // 测试bcrypt cost配置
    }
}
```

**测试用例**:
- [ ] 正确密码验证成功
- [ ] 错误密码验证失败
- [ ] 空密码处理
- [ ] 密码哈希生成
- [ ] bcrypt cost配置
- [ ] 配置获取错误处理

### JWT管理器测试 (jwt_manager.rs)
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_jwt_token() {
        // 测试JWT令牌生成
    }

    #[tokio::test]
    async fn test_validate_jwt_token() {
        // 测试JWT令牌验证
    }

    #[tokio::test]
    async fn test_expired_token_validation() {
        // 测试过期令牌验证
    }

    #[tokio::test]
    async fn test_invalid_token_validation() {
        // 测试无效令牌验证
    }
}
```

**测试用例**:
- [ ] JWT令牌生成成功
- [ ] JWT令牌验证成功
- [ ] 过期令牌验证失败
- [ ] 无效令牌验证失败
- [ ] 用户信息提取正确
- [ ] 令牌格式兼容性

### 令牌管理器测试 (token_manager.rs)
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_generate_api_token() {
        // 测试API令牌生成
    }

    #[tokio::test]
    async fn test_validate_api_token() {
        // 测试API令牌验证
    }

    #[tokio::test]
    async fn test_revoke_token() {
        // 测试令牌撤销
    }
}
```

**测试用例**:
- [ ] API令牌生成成功
- [ ] API令牌验证成功
- [ ] 令牌撤销功能
- [ ] 用户令牌列表获取
- [ ] 过期令牌处理
- [ ] 数据库错误处理

## 集成测试计划

### 认证中间件测试
```rust
#[cfg(test)]
mod integration_tests {
    use super::*;
    use axum_test::TestServer;

    #[tokio::test]
    async fn test_auth_middleware_with_valid_jwt() {
        // 测试有效JWT令牌通过中间件
    }

    #[tokio::test]
    async fn test_auth_middleware_with_valid_api_token() {
        // 测试有效API令牌通过中间件
    }

    #[tokio::test]
    async fn test_auth_middleware_with_invalid_token() {
        // 测试无效令牌被中间件拒绝
    }

    #[tokio::test]
    async fn test_auth_middleware_skip_paths() {
        // 测试跳过认证的路径
    }
}
```

**测试场景**:
- [ ] 有效JWT令牌通过认证
- [ ] 有效API令牌通过认证
- [ ] 无效令牌被拒绝
- [ ] 缺少令牌被拒绝
- [ ] 跳过认证路径正常访问
- [ ] 用户上下文正确传递

### 数据库集成测试
```rust
#[cfg(test)]
mod database_tests {
    use super::*;

    #[tokio::test]
    async fn test_token_crud_operations() {
        // 测试令牌CRUD操作
    }

    #[tokio::test]
    async fn test_user_token_relationship() {
        // 测试用户令牌关联关系
    }

    #[tokio::test]
    async fn test_database_connection_failure() {
        // 测试数据库连接失败处理
    }
}
```

**测试场景**:
- [ ] 令牌创建和存储
- [ ] 令牌查询和验证
- [ ] 令牌更新和撤销
- [ ] 用户令牌关联
- [ ] 数据库连接错误处理
- [ ] 事务处理

## API测试计划

### 登录API测试
```rust
#[tokio::test]
async fn test_login_api_success() {
    let app = create_test_app().await;
    let response = app
        .post("/account/login")
        .json(&json!({"password": "correct_password"}))
        .await;
    
    assert_eq!(response.status(), 200);
    let body: LoginResponse = response.json().await;
    assert_eq!(body.status, "ok");
    assert!(!body.access_token.is_empty());
}

#[tokio::test]
async fn test_login_api_invalid_password() {
    let app = create_test_app().await;
    let response = app
        .post("/account/login")
        .json(&json!({"password": "wrong_password"}))
        .await;
    
    assert_eq!(response.status(), 401);
}
```

**测试用例**:
- [ ] 正确密码登录成功
- [ ] 错误密码登录失败
- [ ] 空密码请求失败
- [ ] 无效JSON格式失败
- [ ] 响应格式正确性
- [ ] JWT令牌有效性

### 用户资料API测试
```rust
#[tokio::test]
async fn test_profile_api_with_valid_token() {
    let app = create_test_app().await;
    let token = get_valid_jwt_token().await;
    
    let response = app
        .get("/account/profile")
        .add_header("Authorization", format!("Bearer {}", token))
        .await;
    
    assert_eq!(response.status(), 200);
    let body: ProfileResponse = response.json().await;
    assert!(!body.id.is_empty());
}
```

**测试用例**:
- [ ] 有效JWT令牌获取资料成功
- [ ] 有效API令牌获取资料成功
- [ ] 无效令牌获取资料失败
- [ ] 缺少认证头获取资料失败
- [ ] 响应格式正确性

### API令牌请求测试
```rust
#[tokio::test]
async fn test_request_access_token_success() {
    let app = create_test_app().await;
    let jwt_token = get_valid_jwt_token().await;
    
    let response = app
        .post("/auth/request_access_token")
        .add_header("Authorization", format!("Bearer {}", jwt_token))
        .json(&json!({"name": "test-token"}))
        .await;
    
    assert_eq!(response.status(), 200);
    let body: TokenResponse = response.json().await;
    assert!(!body.access_token.is_empty());
}
```

**测试用例**:
- [ ] 有效JWT令牌请求API令牌成功
- [ ] 无效JWT令牌请求失败
- [ ] 缺少认证请求失败
- [ ] 生成的API令牌可用
- [ ] 响应格式正确性

### SSO登录测试
```rust
#[tokio::test]
async fn test_sso_login_success() {
    let app = create_test_app().await;
    
    let response = app
        .get("/sso/login/cloud?provider=coco-cloud&product=coco&request_id=12345")
        .await;
    
    assert_eq!(response.status(), 200);
    let body = response.text().await;
    assert!(body.contains("cocoai://oauth_callback"));
}
```

**测试用例**:
- [ ] SSO登录返回HTML页面
- [ ] 重定向URL格式正确
- [ ] 查询参数正确传递
- [ ] 自动重定向逻辑正确
- [ ] JWT令牌包含在重定向URL中

## 兼容性测试计划

### API兼容性测试
```rust
#[tokio::test]
async fn test_api_compatibility_with_go_version() {
    // 使用Go版本的测试用例验证Rust版本
    let test_cases = load_go_version_test_cases();
    
    for test_case in test_cases {
        let response = send_request(&test_case.request).await;
        assert_eq!(response.status(), test_case.expected_status);
        assert_eq!(response.json(), test_case.expected_response);
    }
}
```

**兼容性验证**:
- [ ] API路径完全一致
- [ ] 请求参数格式一致
- [ ] 响应格式完全一致
- [ ] HTTP状态码一致
- [ ] 错误响应格式一致
- [ ] JWT令牌格式兼容

### 客户端兼容性测试
```rust
#[tokio::test]
async fn test_coco_app_client_compatibility() {
    // 模拟Coco App客户端的请求
    let app = create_test_app().await;
    
    // 测试登录流程
    let login_response = simulate_coco_app_login(&app).await;
    assert!(login_response.is_success());
    
    // 测试API调用
    let api_response = simulate_coco_app_api_call(&app, &login_response.token).await;
    assert!(api_response.is_success());
}
```

**客户端测试**:
- [ ] Coco App登录流程
- [ ] Coco App API调用
- [ ] SSO重定向流程
- [ ] 令牌刷新机制
- [ ] 错误处理兼容性

## 性能测试计划

### 基准性能测试
```rust
#[tokio::test]
async fn benchmark_login_performance() {
    let app = create_test_app().await;
    let start = Instant::now();
    
    for _ in 0..100 {
        let response = app
            .post("/account/login")
            .json(&json!({"password": "test_password"}))
            .await;
        assert_eq!(response.status(), 200);
    }
    
    let duration = start.elapsed();
    let avg_duration = duration / 100;
    assert!(avg_duration < Duration::from_millis(500));
}
```

**性能指标**:
- [ ] 登录API响应时间 < 500ms
- [ ] JWT验证时间 < 50ms
- [ ] API令牌验证时间 < 30ms
- [ ] 并发1000请求处理
- [ ] 内存使用优化
- [ ] CPU使用率合理

### 负载测试
```rust
#[tokio::test]
async fn load_test_concurrent_requests() {
    let app = create_test_app().await;
    let mut handles = vec![];
    
    for _ in 0..1000 {
        let app_clone = app.clone();
        let handle = tokio::spawn(async move {
            let response = app_clone
                .post("/account/login")
                .json(&json!({"password": "test_password"}))
                .await;
            response.status() == 200
        });
        handles.push(handle);
    }
    
    let results: Vec<bool> = futures::future::join_all(handles)
        .await
        .into_iter()
        .map(|r| r.unwrap())
        .collect();
    
    let success_rate = results.iter().filter(|&&r| r).count() as f64 / results.len() as f64;
    assert!(success_rate > 0.95); // 95%成功率
}
```

## 安全测试计划

### 认证安全测试
```rust
#[tokio::test]
async fn test_security_invalid_jwt_signature() {
    let app = create_test_app().await;
    let invalid_token = create_jwt_with_invalid_signature();
    
    let response = app
        .get("/account/profile")
        .add_header("Authorization", format!("Bearer {}", invalid_token))
        .await;
    
    assert_eq!(response.status(), 401);
}
```

**安全测试用例**:
- [ ] 无效JWT签名拒绝
- [ ] 过期JWT令牌拒绝
- [ ] 篡改JWT payload拒绝
- [ ] SQL注入防护
- [ ] XSS防护
- [ ] CSRF防护
- [ ] 暴力破解防护

## 测试环境配置

### 测试数据库
```rust
async fn setup_test_database() -> SurrealDBClient {
    let config = DatabaseConfig {
        url: "memory".to_string(),
        namespace: "test".to_string(),
        database: "auth_test".to_string(),
        username: "root".to_string(),
        password: "root".to_string(),
        pool_size: Some(1),
        timeout: Some(5),
    };
    
    SurrealDBClient::new(config).await.unwrap()
}
```

### 测试配置
```rust
fn create_test_config() -> ConfigManager {
    let mut config = Config::default();
    config.auth = Some(AuthConfig {
        jwt_secret: "test-secret-key".to_string(),
        jwt_expiry: 3600,
        api_token_expiry: 86400,
        bcrypt_cost: 4, // 降低测试时的计算成本
        default_user_password: "test_password".to_string(),
    });
    
    ConfigManager::from_config(config)
}
```

## 测试执行计划

### 开发阶段测试
- **每日**: 运行单元测试和基础集成测试
- **每周**: 运行完整测试套件
- **里程碑**: 运行兼容性和性能测试

### CI/CD集成
```yaml
# .github/workflows/test.yml
name: Authentication Module Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Rust
        uses: actions-rs/toolchain@v1
        with:
          toolchain: stable
      - name: Run unit tests
        run: cargo test --lib
      - name: Run integration tests
        run: cargo test --test integration
      - name: Run performance tests
        run: cargo test --test performance
```

### 测试覆盖率目标
- **单元测试覆盖率**: > 90%
- **集成测试覆盖率**: > 85%
- **API测试覆盖率**: 100%
- **兼容性测试**: 100%

## 测试报告

### 自动化报告生成
```bash
# 生成测试覆盖率报告
cargo tarpaulin --out Html

# 生成性能基准报告
cargo bench

# 生成安全审计报告
cargo audit
```

### 测试结果验证
- [ ] 所有单元测试通过
- [ ] 所有集成测试通过
- [ ] API兼容性验证通过
- [ ] 性能指标达标
- [ ] 安全测试通过
- [ ] 代码覆盖率达标
