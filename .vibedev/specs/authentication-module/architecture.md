# 认证模块系统架构设计

## 架构概览

### 系统架构图
```
┌─────────────────────────────────────────────────────────────────┐
│                        Coco Server (Rust)                      │
├─────────────────────────────────────────────────────────────────┤
│  Web Server (9001)          │         API Server (9000)        │
│  ┌─────────────────────────┐ │ ┌─────────────────────────────────┐ │
│  │ SSO Login Handler       │ │ │ Authentication Middleware       │ │
│  │ Static File Handler     │ │ │ ┌─────────────────────────────┐ │ │
│  └─────────────────────────┘ │ │ │ JWT Validator               │ │ │
│                              │ │ │ API Token Validator         │ │ │
│                              │ │ └─────────────────────────────┘ │ │
│                              │ │ Account Handlers                │ │
│                              │ │ ┌─────────────────────────────┐ │ │
│                              │ │ │ Login Handler               │ │ │
│                              │ │ │ Profile Handler             │ │ │
│                              │ │ │ Token Request Handler       │ │ │
│                              │ │ └─────────────────────────────┘ │ │
├──────────────────────────────┼─┼─────────────────────────────────┤ │
│           Authentication Service Layer                          │ │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐ │ │
│ │ Password Manager│ │ JWT Manager     │ │ Token Manager       │ │ │
│ │ - bcrypt hash   │ │ - Generate JWT  │ │ - Generate API Token│ │ │
│ │ - Verify pwd    │ │ - Validate JWT  │ │ - Validate Token    │ │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────────┘ │ │
├─────────────────────────────────────────────────────────────────┤ │
│                    Repository Layer                             │ │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐ │ │
│ │ User Repository │ │ Token Repository│ │ Config Repository   │ │ │
│ │ - CRUD users    │ │ - CRUD tokens   │ │ - Get settings      │ │ │
│ │ - Get by ID     │ │ - Get by token  │ │ - Get secrets       │ │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────────┘ │ │
├─────────────────────────────────────────────────────────────────┤ │
│                      Data Layer                                 │ │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────────┐ │ │
│ │ SurrealDB       │ │ Config Manager  │ │ Memory Cache        │ │ │
│ │ - Users table   │ │ - YAML config   │ │ - JWT cache         │ │ │
│ │ - Tokens table  │ │ - Env variables │ │ - Token cache       │ │ │
│ └─────────────────┘ └─────────────────┘ └─────────────────────┘ │ │
└─────────────────────────────────────────────────────────────────┘ │
```

### 核心组件设计

#### 1. 认证中间件 (Authentication Middleware)
**职责**: 统一处理所有API请求的认证验证
**位置**: `src/middleware/auth_middleware.rs`

```rust
pub struct AuthMiddleware {
    jwt_manager: Arc<JwtManager>,
    token_manager: Arc<TokenManager>,
    config: Arc<ConfigManager>,
}

impl AuthMiddleware {
    // 验证请求认证信息
    pub async fn authenticate<B>(&self, request: Request<B>) -> Result<UserContext, AuthError>
    
    // 检查是否需要跳过认证的路径
    fn should_skip_auth(&self, path: &str) -> bool
    
    // 从请求头提取认证信息
    fn extract_auth_info(&self, headers: &HeaderMap) -> Option<AuthInfo>
}
```

#### 2. JWT管理器 (JWT Manager)
**职责**: 处理JWT令牌的生成、验证和解析
**位置**: `src/auth/jwt_manager.rs`

```rust
pub struct JwtManager {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    validation: Validation,
}

impl JwtManager {
    // 生成JWT访问令牌
    pub fn generate_token(&self, user_info: &UserInfo) -> Result<TokenResponse, JwtError>
    
    // 验证JWT令牌
    pub fn validate_token(&self, token: &str) -> Result<UserClaims, JwtError>
    
    // 解析令牌获取用户信息
    pub fn extract_user_info(&self, token: &str) -> Result<UserContext, JwtError>
}
```

#### 3. 密码管理器 (Password Manager)
**职责**: 处理密码的加密、验证和安全存储
**位置**: `src/auth/password_manager.rs`

```rust
pub struct PasswordManager {
    cost: u32,
}

impl PasswordManager {
    // 验证密码
    pub async fn verify_password(&self, password: &str, hash: &str) -> Result<bool, PasswordError>
    
    // 生成密码哈希
    pub async fn hash_password(&self, password: &str) -> Result<String, PasswordError>
    
    // 从配置获取存储的密码哈希
    pub async fn get_stored_password_hash(&self, config: &ConfigManager) -> Result<String, PasswordError>
}
```

#### 4. 令牌管理器 (Token Manager)
**职责**: 管理API令牌的生命周期
**位置**: `src/auth/token_manager.rs`

```rust
pub struct TokenManager {
    repository: Arc<TokenRepository>,
}

impl TokenManager {
    // 生成API令牌
    pub async fn generate_api_token(&self, user_id: &str, name: &str) -> Result<AccessToken, TokenError>
    
    // 验证API令牌
    pub async fn validate_api_token(&self, token: &str) -> Result<UserContext, TokenError>
    
    // 撤销令牌
    pub async fn revoke_token(&self, token_id: &str, user_id: &str) -> Result<(), TokenError>
    
    // 获取用户的所有令牌
    pub async fn get_user_tokens(&self, user_id: &str) -> Result<Vec<AccessToken>, TokenError>
}
```

### 数据模型设计

#### 用户模型 (User Model)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub username: String,
    pub password_hash: String,
    pub roles: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
    pub last_login: Option<DateTime<Utc>>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub user_id: String,
    pub username: String,
    pub roles: Vec<String>,
}

#[derive(Debug, Clone)]
pub struct UserContext {
    pub user_id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub auth_type: AuthType,
}

#[derive(Debug, Clone)]
pub enum AuthType {
    JWT,
    ApiToken,
}
```

#### 令牌模型 (Token Model)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AccessToken {
    pub id: String,
    pub access_token: String,
    pub user_id: String,
    pub name: String,
    pub provider: String,
    pub token_type: String,
    pub roles: Vec<String>,
    pub permissions: Vec<String>,
    pub expire_in: i64,
    pub created_at: DateTime<Utc>,
    pub last_used: Option<DateTime<Utc>>,
    pub is_active: bool,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserClaims {
    pub user_id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub exp: usize,
    pub iat: usize,
    pub provider: String,
}
```

#### 响应模型 (Response Model)
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub username: String,
    pub id: String,
    pub expire_in: i64,
    pub status: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct TokenResponse {
    pub access_token: String,
    pub expire_in: i64,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ProfileResponse {
    pub id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub last_login: Option<DateTime<Utc>>,
}
```

### 数据库设计

#### SurrealDB表结构

**用户表 (users)**
```sql
DEFINE TABLE users SCHEMAFULL;
DEFINE FIELD id ON TABLE users TYPE string;
DEFINE FIELD username ON TABLE users TYPE string;
DEFINE FIELD password_hash ON TABLE users TYPE string;
DEFINE FIELD roles ON TABLE users TYPE array<string>;
DEFINE FIELD created_at ON TABLE users TYPE datetime;
DEFINE FIELD updated_at ON TABLE users TYPE datetime;
DEFINE FIELD last_login ON TABLE users TYPE option<datetime>;
DEFINE FIELD is_active ON TABLE users TYPE bool DEFAULT true;

DEFINE INDEX idx_username ON TABLE users COLUMNS username UNIQUE;
```

**令牌表 (access_tokens)**
```sql
DEFINE TABLE access_tokens SCHEMAFULL;
DEFINE FIELD id ON TABLE access_tokens TYPE string;
DEFINE FIELD access_token ON TABLE access_tokens TYPE string;
DEFINE FIELD user_id ON TABLE access_tokens TYPE string;
DEFINE FIELD name ON TABLE access_tokens TYPE string;
DEFINE FIELD provider ON TABLE access_tokens TYPE string;
DEFINE FIELD token_type ON TABLE access_tokens TYPE string;
DEFINE FIELD roles ON TABLE access_tokens TYPE array<string>;
DEFINE FIELD permissions ON TABLE access_tokens TYPE array<string>;
DEFINE FIELD expire_in ON TABLE access_tokens TYPE int;
DEFINE FIELD created_at ON TABLE access_tokens TYPE datetime;
DEFINE FIELD last_used ON TABLE access_tokens TYPE option<datetime>;
DEFINE FIELD is_active ON TABLE access_tokens TYPE bool DEFAULT true;

DEFINE INDEX idx_access_token ON TABLE access_tokens COLUMNS access_token UNIQUE;
DEFINE INDEX idx_user_tokens ON TABLE access_tokens COLUMNS user_id;
```

**用户令牌关联表 (user_tokens)**
```sql
DEFINE TABLE user_tokens SCHEMAFULL;
DEFINE FIELD user_id ON TABLE user_tokens TYPE string;
DEFINE FIELD token_ids ON TABLE user_tokens TYPE array<string>;
DEFINE FIELD updated_at ON TABLE user_tokens TYPE datetime;

DEFINE INDEX idx_user_id ON TABLE user_tokens COLUMNS user_id UNIQUE;
```

### API端点设计

#### 认证相关端点

**POST /account/login**
- 功能: 用户密码登录
- 请求体: `{"password": "string"}`
- 响应: `LoginResponse`
- 认证: 无需认证

**GET /account/profile**
- 功能: 获取用户资料
- 响应: `ProfileResponse`
- 认证: JWT或API令牌

**POST /auth/request_access_token**
- 功能: 请求API令牌
- 请求体: `{"name": "string"}`
- 响应: `TokenResponse`
- 认证: JWT令牌

**GET /sso/login/cloud**
- 功能: SSO登录
- 查询参数: provider, product, request_id
- 响应: HTML重定向页面
- 认证: 无需认证

### 安全架构

#### 1. 密码安全
- 使用bcrypt算法，cost=12
- 密码哈希存储在配置系统中
- 支持密码强度验证

#### 2. JWT安全
- 使用HS256算法签名
- 24小时过期时间
- 密钥从环境变量或配置文件获取
- 支持令牌刷新机制

#### 3. API令牌安全
- UUID + 64位随机字符串格式
- 365天过期时间
- 支持令牌撤销
- 记录令牌使用情况

#### 4. 传输安全
- 支持HTTPS（计划中）
- CORS配置
- 安全头部设置

### 错误处理架构

#### 错误类型定义
```rust
#[derive(Error, Debug)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,
    
    #[error("Token expired")]
    TokenExpired,
    
    #[error("Invalid token")]
    InvalidToken,
    
    #[error("Missing authentication")]
    MissingAuth,
    
    #[error("Permission denied")]
    PermissionDenied,
    
    #[error("Database error: {0}")]
    Database(String),
}
```

#### 统一错误响应
```rust
#[derive(Serialize)]
pub struct ErrorResponse {
    pub error: String,
    pub message: String,
    pub code: u16,
}
```

### 配置管理

#### 认证配置结构
```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct AuthConfig {
    pub jwt_secret: String,
    pub jwt_expiry: u64,
    pub api_token_expiry: u64,
    pub bcrypt_cost: u32,
    pub default_user_password: String,
}
```

### 性能优化

#### 1. 缓存策略
- JWT验证结果缓存（内存）
- 用户信息缓存
- 令牌验证缓存

#### 2. 数据库优化
- 索引优化
- 连接池管理
- 查询优化

#### 3. 并发处理
- 异步处理
- 连接复用
- 请求限流
