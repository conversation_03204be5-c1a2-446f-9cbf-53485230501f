# 认证模块实施计划

## 实施概览

本实施计划基于规格驱动开发方法，分为3个主要阶段，确保认证模块的高质量交付。每个阶段都有明确的目标、交付物和质量门控。

## 实施原则

### 1. 质量优先
- 每个功能都必须通过单元测试
- 代码覆盖率必须达到85%以上
- 所有API必须通过兼容性测试

### 2. 增量交付
- 每个任务完成后立即集成测试
- 每周进行一次完整的系统测试
- 持续集成和持续部署

### 3. 兼容性保证
- 严格遵循API规格
- 与Go版本保持完全兼容
- 支持现有客户端无缝迁移

## 阶段1: 基础设施和核心认证 (第1周)

### 目标
建立认证模块的基础架构，实现核心的密码认证和JWT令牌功能。

### 关键交付物
- [ ] 完整的项目结构
- [ ] 密码验证功能
- [ ] JWT令牌管理
- [ ] 登录API端点
- [ ] 基础单元测试

### 详细实施步骤

#### 第1天: 项目结构搭建 (TASK-001)
**时间**: 4小时

**实施步骤**:
1. **更新Cargo.toml依赖**
```toml
[dependencies]
# 新增认证相关依赖
jsonwebtoken = "9.0"
bcrypt = "0.15"
uuid = { version = "1.0", features = ["v4", "serde"] }
chrono = { version = "0.4", features = ["serde"] }
```

2. **创建模块结构**
```
src/auth/
├── mod.rs
├── password_manager.rs
├── jwt_manager.rs
├── token_manager.rs
└── auth_manager.rs

src/models/
├── auth.rs
└── user.rs

src/handlers/
├── auth_handler.rs
└── account_handler.rs (更新)
```

3. **定义基础错误类型**
```rust
// src/error/auth_error.rs
#[derive(Error, Debug)]
pub enum AuthError {
    #[error("Invalid credentials")]
    InvalidCredentials,
    #[error("Token expired")]
    TokenExpired,
    #[error("Invalid token")]
    InvalidToken,
    #[error("Missing authentication")]
    MissingAuth,
}
```

**验收标准**:
- [ ] 项目编译通过
- [ ] 模块导入正确
- [ ] 基础结构完整

#### 第2天: 数据模型定义 (TASK-004)
**时间**: 4小时

**实施步骤**:
1. **定义用户相关模型**
```rust
// src/models/user.rs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct User {
    pub id: String,
    pub username: String,
    pub password_hash: String,
    pub roles: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct UserInfo {
    pub user_id: String,
    pub username: String,
    pub roles: Vec<String>,
}
```

2. **定义认证相关模型**
```rust
// src/models/auth.rs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginRequest {
    pub password: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LoginResponse {
    pub access_token: String,
    pub username: String,
    pub id: String,
    pub expire_in: i64,
    pub status: String,
}
```

**验收标准**:
- [ ] 所有模型定义完整
- [ ] 序列化功能正常
- [ ] 与API规格一致

#### 第3天: 密码管理器实现 (TASK-002)
**时间**: 6小时

**实施步骤**:
1. **实现PasswordManager**
```rust
// src/auth/password_manager.rs
pub struct PasswordManager {
    cost: u32,
}

impl PasswordManager {
    pub fn new(cost: u32) -> Self {
        Self { cost }
    }

    pub async fn verify_password(&self, password: &str, hash: &str) -> Result<bool, AuthError> {
        let password = password.to_string();
        let hash = hash.to_string();
        
        tokio::task::spawn_blocking(move || {
            bcrypt::verify(password, &hash)
        })
        .await
        .map_err(|_| AuthError::Internal("Password verification failed".to_string()))?
        .map_err(|_| AuthError::InvalidCredentials)
    }

    pub async fn get_stored_password_hash(&self, config: &ConfigManager) -> Result<String, AuthError> {
        // 从配置获取存储的密码哈希
        config.get_default_user_password_hash()
            .ok_or(AuthError::ConfigError("Password not configured".to_string()))
    }
}
```

2. **编写单元测试**
```rust
#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_verify_password_success() {
        let manager = PasswordManager::new(4);
        let hash = "$2b$04$..."; // 预计算的测试哈希
        let result = manager.verify_password("test_password", hash).await;
        assert!(result.unwrap());
    }
}
```

**验收标准**:
- [ ] 密码验证功能正常
- [ ] 单元测试通过
- [ ] 错误处理完善

#### 第4天: JWT管理器实现 (TASK-003)
**时间**: 8小时

**实施步骤**:
1. **实现JwtManager**
```rust
// src/auth/jwt_manager.rs
pub struct JwtManager {
    encoding_key: EncodingKey,
    decoding_key: DecodingKey,
    validation: Validation,
}

impl JwtManager {
    pub fn new(secret: &str) -> Self {
        let encoding_key = EncodingKey::from_secret(secret.as_ref());
        let decoding_key = DecodingKey::from_secret(secret.as_ref());
        let validation = Validation::default();
        
        Self {
            encoding_key,
            decoding_key,
            validation,
        }
    }

    pub fn generate_token(&self, user_info: &UserInfo) -> Result<LoginResponse, AuthError> {
        let now = SystemTime::now()
            .duration_since(UNIX_EPOCH)
            .unwrap()
            .as_secs() as usize;

        let claims = UserClaims {
            user_id: user_info.user_id.clone(),
            username: user_info.username.clone(),
            roles: user_info.roles.clone(),
            provider: "simple".to_string(),
            exp: now + 86400, // 24小时
            iat: now,
        };

        let token = encode(&Header::default(), &claims, &self.encoding_key)
            .map_err(|_| AuthError::TokenGeneration)?;

        Ok(LoginResponse {
            access_token: token,
            username: user_info.username.clone(),
            id: user_info.user_id.clone(),
            expire_in: 86400,
            status: "ok".to_string(),
        })
    }
}
```

**验收标准**:
- [ ] JWT生成功能正常
- [ ] JWT验证功能正常
- [ ] 令牌格式兼容
- [ ] 单元测试通过

#### 第5天: 登录API实现 (TASK-005)
**时间**: 6小时

**实施步骤**:
1. **实现登录处理器**
```rust
// src/handlers/account_handler.rs (更新)
pub async fn login_handler(
    State(config_manager): State<Arc<ConfigManager>>,
    Json(payload): Json<LoginRequest>,
) -> Result<Json<LoginResponse>, (StatusCode, Json<ErrorResponse>)> {
    let password_manager = PasswordManager::new(12);
    let jwt_manager = JwtManager::new(&config_manager.get_jwt_secret());

    // 验证密码
    let stored_hash = password_manager
        .get_stored_password_hash(&config_manager)
        .await
        .map_err(|_| (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::internal())))?;

    let is_valid = password_manager
        .verify_password(&payload.password, &stored_hash)
        .await
        .map_err(|_| (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::internal())))?;

    if !is_valid {
        return Err((StatusCode::UNAUTHORIZED, Json(ErrorResponse::invalid_credentials())));
    }

    // 生成JWT令牌
    let user_info = get_default_user_info().await;
    let response = jwt_manager
        .generate_token(&user_info)
        .map_err(|_| (StatusCode::INTERNAL_SERVER_ERROR, Json(ErrorResponse::internal())))?;

    Ok(Json(response))
}
```

2. **集成测试**
```rust
#[tokio::test]
async fn test_login_api_integration() {
    let app = create_test_app().await;
    
    let response = app
        .post("/account/login")
        .json(&json!({"password": "correct_password"}))
        .await;
    
    assert_eq!(response.status(), 200);
    let body: LoginResponse = response.json().await;
    assert_eq!(body.status, "ok");
}
```

**验收标准**:
- [ ] API端点功能正常
- [ ] 响应格式正确
- [ ] 集成测试通过

### 阶段1质量门控

**质量标准**:
- [ ] 代码覆盖率 ≥ 85%
- [ ] 所有单元测试通过
- [ ] 集成测试通过
- [ ] 登录API功能正常
- [ ] 密码验证安全性验证
- [ ] JWT令牌格式兼容性验证

**门控检查**:
```bash
# 运行测试
cargo test

# 检查覆盖率
cargo tarpaulin --out Html

# 代码质量检查
cargo clippy -- -D warnings

# 格式检查
cargo fmt --check
```

## 阶段2: 令牌管理和中间件 (第2周)

### 目标
实现完整的令牌管理系统，包括API令牌和认证中间件。

### 关键交付物
- [ ] SurrealDB集成
- [ ] API令牌管理
- [ ] 认证中间件
- [ ] 用户资料API
- [ ] API令牌请求端点

### 详细实施步骤

#### 第6-7天: SurrealDB集成 (TASK-006)
**时间**: 8小时

**实施步骤**:
1. **设计数据库表结构**
```sql
-- 用户表
DEFINE TABLE users SCHEMAFULL;
DEFINE FIELD id ON TABLE users TYPE string;
DEFINE FIELD username ON TABLE users TYPE string;
DEFINE FIELD password_hash ON TABLE users TYPE string;
DEFINE FIELD roles ON TABLE users TYPE array<string>;
DEFINE FIELD created_at ON TABLE users TYPE datetime;
DEFINE FIELD updated_at ON TABLE users TYPE datetime;

-- 令牌表
DEFINE TABLE access_tokens SCHEMAFULL;
DEFINE FIELD id ON TABLE access_tokens TYPE string;
DEFINE FIELD access_token ON TABLE access_tokens TYPE string;
DEFINE FIELD user_id ON TABLE access_tokens TYPE string;
DEFINE FIELD name ON TABLE access_tokens TYPE string;
DEFINE FIELD expire_in ON TABLE access_tokens TYPE int;
DEFINE FIELD created_at ON TABLE access_tokens TYPE datetime;
```

2. **实现Repository层**
```rust
// src/repositories/token_repository.rs
pub struct TokenRepository {
    db: Arc<SurrealDBClient>,
}

impl TokenRepository {
    pub async fn create_token(&self, token: &AccessToken) -> Result<(), DatabaseError> {
        self.db.create("access_tokens", token).await
    }

    pub async fn get_token_by_value(&self, token: &str) -> Result<Option<AccessToken>, DatabaseError> {
        let query = "SELECT * FROM access_tokens WHERE access_token = $token";
        let mut result = self.db.query_with_params(query, &json!({"token": token})).await?;
        result.take(0)
    }
}
```

**验收标准**:
- [ ] 数据库连接正常
- [ ] 表结构创建成功
- [ ] CRUD操作功能正常

#### 第8-9天: API令牌管理器 (TASK-007)
**时间**: 8小时

**实施步骤**:
1. **实现TokenManager**
```rust
// src/auth/token_manager.rs
pub struct TokenManager {
    repository: Arc<TokenRepository>,
}

impl TokenManager {
    pub async fn generate_api_token(&self, user_id: &str, name: &str) -> Result<AccessToken, TokenError> {
        let token_value = format!("{}-{}", Uuid::new_v4(), generate_random_string(64));
        let expire_in = Utc::now().timestamp() + 365 * 24 * 3600; // 365天

        let token = AccessToken {
            id: Uuid::new_v4().to_string(),
            access_token: token_value.clone(),
            user_id: user_id.to_string(),
            name: name.to_string(),
            provider: "access_token".to_string(),
            token_type: "api".to_string(),
            roles: vec![],
            permissions: vec![],
            expire_in,
            created_at: Utc::now(),
            last_used: None,
            is_active: true,
        };

        self.repository.create_token(&token).await?;
        Ok(token)
    }
}
```

**验收标准**:
- [ ] 令牌生成功能正常
- [ ] 令牌验证功能正常
- [ ] 数据库存储正常

#### 第10天: 认证中间件 (TASK-008)
**时间**: 6小时

**实施步骤**:
1. **实现认证中间件**
```rust
// src/middleware/auth_middleware.rs
pub async fn auth_middleware<B>(
    request: Request<B>,
    next: Next<B>,
) -> Result<Response, impl IntoResponse>
where
    B: Send,
{
    let path = request.uri().path();
    if should_skip_auth(path) {
        return Ok(next.run(request).await);
    }

    // 提取认证信息
    let auth_info = extract_auth_info(request.headers());
    
    match auth_info {
        Some(AuthInfo::Bearer(token)) => {
            // 验证JWT令牌
            match validate_jwt_token(&token).await {
                Ok(user_context) => {
                    // 将用户上下文添加到请求中
                    let mut request = request;
                    request.extensions_mut().insert(user_context);
                    Ok(next.run(request).await)
                }
                Err(_) => Err(unauthorized_response()),
            }
        }
        Some(AuthInfo::ApiToken(token)) => {
            // 验证API令牌
            match validate_api_token(&token).await {
                Ok(user_context) => {
                    let mut request = request;
                    request.extensions_mut().insert(user_context);
                    Ok(next.run(request).await)
                }
                Err(_) => Err(unauthorized_response()),
            }
        }
        None => Err(unauthorized_response()),
    }
}
```

**验收标准**:
- [ ] 中间件功能正常
- [ ] 多种认证方式支持
- [ ] 路径跳过功能正常

### 阶段2质量门控

**质量标准**:
- [ ] 代码覆盖率 ≥ 85%
- [ ] 所有API端点功能正常
- [ ] 数据库集成测试通过
- [ ] 认证中间件测试通过
- [ ] 性能基准测试通过

## 阶段3: SSO和完善 (第3周)

### 目标
完成SSO功能，进行性能优化和全面测试。

### 关键交付物
- [ ] SSO登录功能
- [ ] 性能优化
- [ ] 完整测试套件
- [ ] 兼容性验证
- [ ] 生产就绪

### 详细实施步骤

#### 第11-12天: SSO登录实现 (TASK-011)
**时间**: 6小时

#### 第13天: 性能优化 (TASK-014)
**时间**: 6小时

#### 第14-15天: 测试和文档 (TASK-015)
**时间**: 8小时

### 阶段3质量门控

**质量标准**:
- [ ] 整体质量评分 ≥ 95%
- [ ] 所有功能测试通过
- [ ] 性能指标达标
- [ ] 兼容性验证通过
- [ ] 安全测试通过

## 风险管理

### 技术风险
1. **SurrealDB集成复杂性**
   - 缓解: 提前验证功能，准备备选方案
   - 应急计划: 使用内存存储作为临时方案

2. **性能不达预期**
   - 缓解: 持续性能监控，及时优化
   - 应急计划: 降级到基础功能

### 进度风险
1. **任务延期**
   - 缓解: 每日进度检查，及时调整
   - 应急计划: 调整任务优先级

2. **质量问题**
   - 缓解: 严格的质量门控
   - 应急计划: 增加测试和修复时间

## 交付标准

### 代码质量
- [ ] 代码覆盖率 ≥ 85%
- [ ] 所有clippy警告解决
- [ ] 代码格式符合标准
- [ ] 文档注释完整

### 功能完整性
- [ ] 所有API端点实现
- [ ] 所有用户故事完成
- [ ] 兼容性测试通过
- [ ] 性能指标达标

### 生产就绪
- [ ] 错误处理完善
- [ ] 日志记录完整
- [ ] 监控指标配置
- [ ] 部署文档完整
