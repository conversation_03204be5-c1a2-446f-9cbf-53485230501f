# 认证模块需求规格

## 执行摘要
本项目旨在为Rust重写的Coco Server实现完整的认证模块，确保与原Go版本的API兼容性。认证模块将提供用户登录、JWT令牌管理、API令牌管理和会话管理功能，支持多种认证方式以满足不同客户端的需求。

## 利益相关者
- **Coco AI客户端用户**: 需要通过认证访问服务器功能
- **API集成开发者**: 需要使用API令牌进行程序化访问
- **系统管理员**: 需要管理用户账户和令牌
- **Coco App开发团队**: 需要确保客户端能正常连接服务器

## 功能性需求

### FR-001: 用户登录认证
**描述**: 实现基于密码的用户登录功能，生成JWT访问令牌
**优先级**: 高
**验收标准**:
- [ ] 支持POST /account/login端点
- [ ] 接受JSON格式的密码参数
- [ ] 验证密码正确性（使用bcrypt）
- [ ] 生成JWT访问令牌（24小时有效期）
- [ ] 返回标准格式的登录响应
- [ ] 密码错误时返回401状态码

### FR-002: JWT令牌验证
**描述**: 验证JWT访问令牌的有效性和完整性
**优先级**: 高
**验收标准**:
- [ ] 支持Bearer Authorization头验证
- [ ] 验证JWT签名和过期时间
- [ ] 提取用户信息（用户ID、用户名、角色）
- [ ] 令牌无效时返回401状态码
- [ ] 令牌过期时返回401状态码

### FR-003: API令牌管理
**描述**: 管理长期有效的API令牌，用于程序化访问
**优先级**: 高
**验收标准**:
- [ ] 支持POST /auth/request_access_token端点
- [ ] 生成唯一的API令牌（365天有效期）
- [ ] 支持X-API-TOKEN头验证
- [ ] 令牌与用户账户关联
- [ ] 支持令牌撤销功能

### FR-004: 用户资料管理
**描述**: 获取和管理用户基本信息
**优先级**: 中
**验收标准**:
- [ ] 支持GET /account/profile端点
- [ ] 返回用户基本信息（ID、用户名、角色）
- [ ] 需要有效的认证令牌
- [ ] 支持用户信息更新

### FR-005: SSO登录支持
**描述**: 支持单点登录，特别是与Coco Cloud的集成
**优先级**: 中
**验收标准**:
- [ ] 支持GET /sso/login/cloud端点
- [ ] 处理SSO回调参数（provider、product、request_id）
- [ ] 生成重定向URL到cocoai://oauth_callback
- [ ] 返回HTML页面支持自动重定向

### FR-006: 密码管理
**描述**: 支持密码修改和验证
**优先级**: 中
**验收标准**:
- [ ] 支持密码修改功能
- [ ] 使用bcrypt加密存储密码
- [ ] 验证当前密码正确性
- [ ] 密码强度验证

## 非功能性需求

### NFR-001: 性能要求
**描述**: 认证操作的性能指标
**指标**:
- 登录API响应时间 < 500毫秒
- JWT验证时间 < 50毫秒
- API令牌验证时间 < 30毫秒
- 支持并发1000个认证请求

### NFR-002: 安全性要求
**描述**: 认证模块的安全标准
**标准**:
- 使用bcrypt加密密码（cost=12）
- JWT使用HS256算法签名
- 密钥安全存储（环境变量或配置文件）
- 防止暴力破解攻击
- 令牌泄露时支持快速撤销

### NFR-003: 兼容性要求
**描述**: 与现有系统的兼容性
**标准**:
- API路径与Go版本完全一致
- 响应格式与Go版本完全一致
- JWT令牌格式兼容
- 支持现有客户端无缝迁移

### NFR-004: 可靠性要求
**描述**: 系统可靠性指标
**标准**:
- 认证服务可用性 > 99.9%
- 数据库连接失败时优雅降级
- 错误日志记录完整
- 支持健康检查

## 约束条件
- 必须使用SurrealDB替代Elasticsearch存储
- 必须保持与Go版本API的完全兼容性
- 必须支持现有Coco App客户端
- 使用Rust语言和Axum框架实现
- 遵循现有项目的代码结构和风格

## 假设条件
- SurrealDB已正确配置和连接
- 配置管理系统已实现
- 基础的HTTP服务器框架已搭建
- 错误处理机制已建立

## 范围外
- 多因素认证（MFA）
- OAuth2第三方登录
- 用户注册功能
- 密码重置功能
- 审计日志功能
- 角色权限管理（RBAC）详细实现

## 数据模型需求

### 用户信息结构
```rust
pub struct UserInfo {
    pub user_id: String,
    pub username: String,
    pub password_hash: String,
    pub roles: Vec<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,
}
```

### JWT Claims结构
```rust
pub struct UserClaims {
    pub user_id: String,
    pub username: String,
    pub roles: Vec<String>,
    pub exp: usize,
}
```

### API令牌结构
```rust
pub struct AccessToken {
    pub id: String,
    pub access_token: String,
    pub user_id: String,
    pub name: String,
    pub provider: String,
    pub token_type: String,
    pub roles: Vec<String>,
    pub expire_in: i64,
    pub created_at: DateTime<Utc>,
}
```

## API端点规格

### POST /account/login
- 请求体: `{"password": "string"}`
- 响应: `{"access_token": "string", "username": "string", "id": "string", "expire_in": number, "status": "ok"}`

### GET /account/profile
- 认证: Bearer Token或X-API-TOKEN
- 响应: 用户基本信息

### POST /auth/request_access_token
- 认证: Bearer Token
- 响应: `{"access_token": "string", "expire_in": number}`

### GET /sso/login/cloud
- 查询参数: provider, product, request_id
- 响应: HTML重定向页面
