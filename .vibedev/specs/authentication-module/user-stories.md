# 认证模块用户故事

## 史诗: 用户认证与授权

### 故事: AUTH-001 - 用户密码登录
**作为** Coco AI客户端用户
**我希望** 使用密码登录系统
**以便** 获得访问服务器功能的权限

**验收标准** (EARS格式):
- **当** 用户提供正确密码时 **那么** 系统返回有效的JWT访问令牌
- **当** 用户提供错误密码时 **那么** 系统返回401错误和相应错误信息
- **如果** 密码为空或格式无效 **那么** 系统返回400错误
- **对于** 登录响应 **验证** 包含access_token、username、id、expire_in和status字段

**技术说明**:
- 使用bcrypt验证密码
- JWT令牌24小时有效期
- 响应格式与Go版本完全一致

**故事点数**: 5
**优先级**: 高

### 故事: AUTH-002 - JWT令牌验证
**作为** API客户端
**我希望** 使用JWT令牌访问受保护的API
**以便** 在不重复登录的情况下调用服务

**验收标准** (EARS格式):
- **当** 提供有效JWT令牌时 **那么** 系统允许访问受保护资源
- **当** 提供过期JWT令牌时 **那么** 系统返回401错误
- **当** 提供无效JWT令牌时 **那么** 系统返回401错误
- **如果** 未提供令牌 **那么** 系统返回401错误要求认证

**技术说明**:
- 支持Authorization: Bearer <token>格式
- 验证JWT签名和过期时间
- 提取用户信息用于后续处理

**故事点数**: 3
**优先级**: 高

### 故事: AUTH-003 - API令牌生成
**作为** 开发者
**我希望** 生成长期有效的API令牌
**以便** 在应用程序中进行自动化API调用

**验收标准** (EARS格式):
- **当** 使用有效JWT令牌请求API令牌时 **那么** 系统生成新的API令牌
- **当** 未提供认证时 **那么** 系统返回401错误
- **对于** API令牌 **验证** 365天有效期且与用户账户关联
- **对于** 响应 **验证** 包含access_token和expire_in字段

**技术说明**:
- API令牌格式: UUID + 64位随机字符串
- 存储在SurrealDB中
- 支持令牌撤销

**故事点数**: 5
**优先级**: 高

### 故事: AUTH-004 - API令牌验证
**作为** API客户端
**我希望** 使用API令牌访问服务
**以便** 进行长期的程序化访问

**验收标准** (EARS格式):
- **当** 提供有效API令牌时 **那么** 系统允许访问受保护资源
- **当** 提供无效API令牌时 **那么** 系统返回401错误
- **当** 提供过期API令牌时 **那么** 系统返回401错误
- **对于** X-API-TOKEN头 **验证** 正确解析和验证

**技术说明**:
- 支持X-API-TOKEN头格式
- 从数据库验证令牌有效性
- 提取关联的用户信息

**故事点数**: 3
**优先级**: 高

### 故事: AUTH-005 - 用户资料获取
**作为** 认证用户
**我希望** 获取我的用户资料信息
**以便** 在客户端显示用户信息

**验收标准** (EARS格式):
- **当** 提供有效认证令牌时 **那么** 系统返回用户基本信息
- **当** 未提供认证时 **那么** 系统返回401错误
- **对于** 用户资料 **验证** 包含用户ID、用户名和角色信息
- **对于** 响应格式 **验证** 与Go版本API兼容

**技术说明**:
- 支持JWT和API令牌认证
- 从令牌中提取用户信息
- 不返回敏感信息如密码

**故事点数**: 2
**优先级**: 中

### 故事: AUTH-006 - SSO登录支持
**作为** Coco Cloud用户
**我希望** 通过SSO方式登录
**以便** 无缝集成云服务和本地服务

**验收标准** (EARS格式):
- **当** 访问SSO登录端点时 **那么** 系统生成JWT令牌并重定向
- **当** 提供SSO参数时 **那么** 系统在重定向URL中包含这些参数
- **对于** 重定向URL **验证** 使用cocoai://oauth_callback协议
- **对于** HTML响应 **验证** 包含自动重定向和手动链接

**技术说明**:
- 处理provider、product、request_id参数
- 生成HTML页面支持自动重定向
- 兼容Coco App的URL scheme

**故事点数**: 5
**优先级**: 中

### 故事: AUTH-007 - 认证中间件
**作为** 系统开发者
**我希望** 有统一的认证中间件
**以便** 保护所有需要认证的API端点

**验收标准** (EARS格式):
- **当** 请求受保护端点时 **那么** 中间件自动验证认证信息
- **当** 认证失败时 **那么** 中间件返回统一的错误响应
- **如果** 是公开端点 **那么** 中间件跳过认证检查
- **对于** 认证成功 **验证** 用户信息传递给后续处理器

**技术说明**:
- 支持多种认证方式（JWT、API令牌）
- 可配置的跳过路径列表
- 统一的错误响应格式

**故事点数**: 3
**优先级**: 高

### 故事: AUTH-008 - 密码验证
**作为** 系统
**我希望** 安全地验证用户密码
**以便** 确保只有授权用户能够登录

**验收标准** (EARS格式):
- **当** 验证密码时 **那么** 使用bcrypt算法比较哈希值
- **当** 密码不匹配时 **那么** 返回验证失败
- **对于** 密码存储 **验证** 使用适当的bcrypt cost值
- **对于** 验证过程 **验证** 防止时序攻击

**技术说明**:
- 使用bcrypt库进行密码验证
- 从配置管理器获取存储的密码哈希
- 适当的错误处理和日志记录

**故事点数**: 2
**优先级**: 高

## 史诗: 令牌管理

### 故事: TOKEN-001 - 令牌存储管理
**作为** 系统
**我希望** 安全地存储和管理各种令牌
**以便** 支持令牌的生命周期管理

**验收标准** (EARS格式):
- **当** 生成新令牌时 **那么** 系统将其存储在SurrealDB中
- **当** 验证令牌时 **那么** 系统从数据库查询令牌信息
- **当** 令牌过期时 **那么** 系统自动标记为无效
- **对于** 令牌数据 **验证** 包含完整的元数据信息

**技术说明**:
- 使用SurrealDB存储令牌
- 支持令牌过期自动清理
- 维护用户与令牌的关联关系

**故事点数**: 5
**优先级**: 中

### 故事: TOKEN-002 - 令牌撤销
**作为** 用户或管理员
**我希望** 能够撤销已发放的令牌
**以便** 在令牌泄露时保护账户安全

**验收标准** (EARS格式):
- **当** 请求撤销令牌时 **那么** 系统立即使令牌无效
- **当** 使用已撤销令牌时 **那么** 系统返回401错误
- **对于** 令牌撤销 **验证** 更新数据库中的令牌状态
- **对于** 用户令牌列表 **验证** 移除已撤销的令牌

**技术说明**:
- 支持单个令牌撤销
- 更新数据库中的令牌状态
- 清理用户令牌关联关系

**故事点数**: 3
**优先级**: 中
