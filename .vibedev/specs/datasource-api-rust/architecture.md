# DataSource API 系统架构设计

## 1. 系统概述

### 1.1 架构目标
本架构设计旨在将Go版本的DataSource API重写为Rust版本，保持功能完全一致的同时，利用Rust语言的优势提供更好的性能、内存安全和并发处理能力。

### 1.2 设计原则
- **兼容性优先**: 与Go版本API完全兼容
- **性能优化**: 利用Rust的零成本抽象和内存安全
- **模块化设计**: 清晰的模块边界和职责分离
- **异步优先**: 基于Tokio异步运行时
- **类型安全**: 充分利用Rust的类型系统

### 1.3 系统边界
- **输入**: HTTP请求（JSON格式）
- **输出**: HTTP响应（JSON格式）
- **存储**: Elasticsearch数据库
- **缓存**: 内存缓存系统
- **认证**: API Token验证

## 2. 总体架构

### 2.1 分层架构

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Client Layer)                    │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │  Web UI     │  │  API Client │  │  第三方集成 │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     API网关层 (API Gateway)                   │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                Axum Router & Middleware                │ │
│  │  - 路由管理  - 认证中间件  - CORS中间件  - 错误处理     │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    业务逻辑层 (Business Layer)                 │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                 DataSource Handlers                      │ │
│  │  - CRUD操作  - 搜索功能  - 文档管理  - 验证逻辑         │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   数据访问层 (Data Access Layer)              │
│  ┌─────────────────────────────────────────────────────────┐ │
│  │                  Repository Layer                        │ │
│  │  - Elasticsearch客户端  - 数据模型  - 查询构建          │ │
│  └─────────────────────────────────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   基础设施层 (Infrastructure Layer)            │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐         │
│  │Elasticsearch│  │  缓存系统   │  │  日志系统   │         │
│  └─────────────┘  └─────────────┘  └─────────────┘         │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 组件交互图

```
┌─────────────┐    HTTP     ┌─────────────┐    调用     ┌─────────────┐
│   客户端    │ ──────────> │  API网关    │ ──────────> │  业务逻辑    │
└─────────────┘             └─────────────┘             └─────────────┘
       │                        │                           │
       │                        │                           │
       │                   认证/权限                   业务验证
       │                        │                           │
       │                        ▼                           ▼
┌─────────────┐             ┌─────────────┐             ┌─────────────┐
│   响应      │ <───────── │  响应处理    │ <───────── │  数据访问    │
└─────────────┘             └─────────────┘             └─────────────┘
                                                        │
                                                        ▼
                                                 ┌─────────────┐
                                                 │  数据存储    │
                                                 └─────────────┘
```

## 3. 模块设计

### 3.1 核心模块结构

```
src/
├── main.rs                    # 应用程序入口
├── handlers/                  # 请求处理器模块
│   ├── mod.rs                # 处理器模块导出
│   ├── datasource_handler.rs # 数据源相关处理器
│   ├── models.rs             # 通用数据模型
│   └── ...                   # 其他处理器
├── repositories/             # 数据访问层
│   ├── mod.rs                # 仓库模块导出
│   ├── datasource_repo.rs   # 数据源仓库
│   ├── cache_manager.rs      # 缓存管理
│   └── elasticsearch.rs      # ES客户端封装
├── models/                   # 数据模型
│   ├── mod.rs                # 模型模块导出
│   ├── datasource.rs         # 数据源模型
│   ├── connector.rs          # 连接器模型
│   └── document.rs           # 文档模型
├── services/                 # 业务服务
│   ├── mod.rs                # 服务模块导出
│   ├── datasource_service.rs # 数据源服务
│   ├── validation_service.rs # 验证服务
│   └── cache_service.rs      # 缓存服务
├── middleware/               # 中间件
│   ├── mod.rs                # 中间件模块导出
│   ├── auth.rs               # 认证中间件
│   ├── cors.rs               # CORS中间件
│   └── error_handling.rs     # 错误处理中间件
├── utils/                    # 工具函数
│   ├── mod.rs                # 工具模块导出
│   ├── errors.rs             # 错误定义
│   ├── response.rs           # 响应工具
│   └── validation.rs         # 验证工具
└── config/                   # 配置管理
    ├── mod.rs                # 配置模块导出
    ├── app_config.rs         # 应用配置
    └── database_config.rs    # 数据库配置
```

### 3.2 关键模块详细设计

#### 3.2.1 Handlers模块
**职责**: 处理HTTP请求，调用业务逻辑，返回HTTP响应

```rust
// datasource_handler.rs
pub struct DataSourceHandler {
    datasource_service: Arc<DataSourceService>,
}

impl DataSourceHandler {
    // CRUD操作
    pub async fn create_datasource(
        &self,
        request: CreateDataSourceRequest,
    ) -> Result<CreateDataSourceResponse, APIError>;
    
    pub async fn get_datasource(
        &self,
        id: &str,
    ) -> Result<GetDataSourceResponse, APIError>;
    
    pub async fn update_datasource(
        &self,
        id: &str,
        request: UpdateDataSourceRequest,
    ) -> Result<UpdateDataSourceResponse, APIError>;
    
    pub async fn delete_datasource(
        &self,
        id: &str,
    ) -> Result<DeleteDataSourceResponse, APIError>;
    
    // 搜索操作
    pub async fn search_datasources(
        &self,
        query: SearchQuery,
    ) -> Result<SearchResponse, APIError>;
    
    // 文档管理
    pub async fn create_document(
        &self,
        datasource_id: &str,
        document: CreateDocumentRequest,
    ) -> Result<CreateDocumentResponse, APIError>;
}
```

#### 3.2.2 Repositories模块
**职责**: 数据访问层，封装数据库操作和缓存逻辑

```rust
// datasource_repo.rs
pub struct DataSourceRepository {
    elasticsearch: Arc<ElasticsearchClient>,
    cache_manager: Arc<CacheManager>,
}

impl DataSourceRepository {
    // CRUD操作
    pub async fn create(&self, datasource: &DataSource) -> Result<String, RepositoryError>;
    pub async fn get_by_id(&self, id: &str) -> Result<Option<DataSource>, RepositoryError>;
    pub async fn update(&self, id: &str, datasource: &DataSource) -> Result<(), RepositoryError>;
    pub async fn delete(&self, id: &str) -> Result<(), RepositoryError>;
    
    // 搜索操作
    pub async fn search(&self, query: &SearchQuery) -> Result<SearchResult, RepositoryError>;
    
    // 缓存操作
    pub async fn get_from_cache(&self, id: &str) -> Result<Option<DataSource>, RepositoryError>;
    pub async fn set_cache(&self, id: &str, datasource: &DataSource) -> Result<(), RepositoryError>;
    pub async fn invalidate_cache(&self, id: &str) -> Result<(), RepositoryError>;
}
```

#### 3.2.3 Services模块
**职责**: 业务逻辑处理，数据验证，跨服务协调

```rust
// datasource_service.rs
pub struct DataSourceService {
    datasource_repo: Arc<DataSourceRepository>,
    validation_service: Arc<ValidationService>,
    cache_service: Arc<CacheService>,
}

impl DataSourceService {
    // 业务逻辑
    pub async fn create_datasource(
        &self,
        request: CreateDataSourceRequest,
    ) -> Result<DataSource, ServiceError> {
        // 1. 验证请求数据
        self.validation_service.validate_create_request(&request)?;
        
        // 2. 验证连接器存在性
        self.validate_connector_exists(&request.connector.id).await?;
        
        // 3. 创建数据源
        let datasource = self.build_datasource_from_request(request)?;
        let id = self.datasource_repo.create(&datasource).await?;
        
        // 4. 清理相关缓存
        self.cache_service.invalidate_datasource_caches().await?;
        
        Ok(datasource.with_id(id))
    }
    
    // 其他业务方法...
}
```

#### 3.2.4 Models模块
**职责**: 定义数据结构和业务实体

```rust
// datasource.rs
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DataSource {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub id: Option<String>,
    pub r#type: String,
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub icon: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub category: Option<String>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub tags: Option<Vec<String>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub connector: Option<ConnectorConfig>,
    #[serde(default = "default_sync_enabled")]
    pub sync_enabled: bool,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub created: Option<DateTime<Utc>>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub updated: Option<DateTime<Utc>>,
}

// 请求/响应模型
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateDataSourceRequest {
    pub name: String,
    pub r#type: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub description: Option<String>,
    pub connector: ConnectorConfig,
    #[serde(default = "default_sync_enabled")]
    pub sync_enabled: bool,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
}
```

## 4. 数据架构

### 4.1 数据模型设计

#### 4.1.1 Elasticsearch索引映射
```json
{
  "mappings": {
    "properties": {
      "id": {
        "type": "keyword",
        "copy_to": "combined_fulltext"
      },
      "type": {
        "type": "keyword",
        "copy_to": "combined_fulltext"
      },
      "name": {
        "type": "keyword",
        "copy_to": "combined_fulltext"
      },
      "description": {
        "type": "text",
        "copy_to": "combined_fulltext"
      },
      "icon": {
        "enabled": false
      },
      "category": {
        "type": "keyword"
      },
      "tags": {
        "type": "keyword"
      },
      "connector": {
        "type": "object",
        "properties": {
          "id": {
            "type": "keyword"
          },
          "config": {
            "enabled": false
          }
        }
      },
      "sync_enabled": {
        "type": "keyword"
      },
      "enabled": {
        "type": "keyword"
      },
      "combined_fulltext": {
        "type": "text"
      },
      "created": {
        "type": "date"
      },
      "updated": {
        "type": "date"
      }
    }
  }
}
```

#### 4.1.2 数据关系设计
```
DataSource (1) ── (N) Document
    │                     │
    │                     │
    │                     │
Connector (1) ── (N) DataSource
```

### 4.2 缓存架构

#### 4.2.1 缓存策略
- **内存缓存**: 使用Rust的`dashmap`或`tokio::sync::RwLock`
- **缓存键设计**: 使用分层键名空间
- **过期策略**: 基于时间的TTL策略
- **失效策略**: 主动失效 + 被动失效

#### 4.2.2 缓存键规范
```rust
// 缓存键定义
pub const DATASOURCE_PRIMARY_CACHE_KEY: &str = "datasource_primary";
pub const DISABLED_DATASOURCE_IDS_CACHE_KEY: &str = "disabled_datasource_ids";
pub const ENABLED_DATASOURCE_IDS_CACHE_KEY: &str = "enabled_datasource_ids";
pub const DATASOURCE_ITEMS_CACHE_KEY: &str = "datasource_items";

// 缓存键格式
fn build_cache_key(namespace: &str, key: &str) -> String {
    format!("{}:{}", namespace, key)
}
```

## 5. API架构

### 5.1 RESTful API设计

#### 5.1.1 API端点映射
| Go版本端点 | Rust版本端点 | HTTP方法 | 功能 |
|-----------|-------------|---------|------|
| `/datasource/` | `/datasource/` | POST | 创建数据源 |
| `/datasource/:id` | `/datasource/:id` | GET | 获取数据源 |
| `/datasource/:id` | `/datasource/:id` | PUT | 更新数据源 |
| `/datasource/:id` | `/datasource/:id` | DELETE | 删除数据源 |
| `/datasource/_search` | `/datasource/_search` | GET | 搜索数据源 |
| `/datasource/_search` | `/datasource/_search` | POST | 搜索数据源 |
| `/datasource/_search` | `/datasource/_search` | OPTIONS | CORS预检 |
| `/datasource/:id/_doc` | `/datasource/:id/_doc` | POST | 创建文档 |
| `/datasource/:id/_doc/:doc_id` | `/datasource/:id/_doc/:doc_id` | POST | 创建指定ID文档 |

#### 5.1.2 请求/响应架构
```rust
// 请求处理器签名
pub async fn create_datasource_handler(
    State(config): State<Arc<Config>>,
    Json(request): Json<CreateDataSourceRequest>,
) -> Result<Json<CreateDataSourceResponse>, APIError> {
    // 处理逻辑...
}

// 响应结构体
#[derive(Debug, Serialize, Deserialize)]
pub struct CreateDataSourceResponse {
    #[serde(rename = "_id")]
    pub id: String,
    pub result: String,
}
```

### 5.2 中间件架构

#### 5.2.1 中间件链
```rust
// 中间件栈
Router::new()
    .route("/datasource/", post(create_datasource_handler))
    .route_layer(middleware::from_fn_with_state(
        config.clone(),
        auth_middleware,
    ))
    .route_layer(middleware::from_fn_with_state(
        config.clone(),
        permission_middleware,
    ))
    .route_layer(middleware::from_fn(cors_middleware))
    .route_layer(middleware::from_fn(error_handling_middleware))
    .with_state(config)
```

#### 5.2.2 认证中间件
```rust
pub async fn auth_middleware(
    State(config): State<Arc<Config>>,
    req: Request,
    next: Next,
) -> Result<Response, StatusCode> {
    // 1. 提取认证令牌
    let token = extract_token_from_request(&req)?;
    
    // 2. 验证令牌
    let claims = validate_token(&token, &config.auth_secret)?;
    
    // 3. 将用户信息添加到请求扩展中
    let mut req = req;
    req.extensions_mut().insert(UserContext::from(claims));
    
    // 4. 继续处理请求
    Ok(next.run(req).await)
}
```

## 6. 性能架构

### 6.1 并发模型
- **异步运行时**: Tokio多线程运行时
- **连接池**: 数据库连接池复用
- **内存管理**: Rust所有权系统保证内存安全
- **零拷贝**: 尽可能避免数据拷贝

### 6.2 缓存策略
- **多级缓存**: 内存缓存 + 数据库查询缓存
- **缓存预热**: 系统启动时预热热点数据
- **缓存击穿保护**: 使用互斥锁防止缓存击穿
- **缓存雪崩保护**: 随机过期时间

### 6.3 数据库优化
- **批量操作**: 支持批量文档创建
- **查询优化**: 使用Elasticsearch查询优化
- **索引优化**: 合理的索引设计和分片策略
- **连接池**: Elasticsearch连接池管理

## 7. 安全架构

### 7.1 认证架构
```rust
// JWT令牌验证
pub struct JWTAuthenticator {
    secret: String,
}

impl JWTAuthenticator {
    pub fn validate_token(&self, token: &str) -> Result<Claims, AuthError> {
        // JWT验证逻辑
    }
}
```

### 7.2 权限架构
```rust
// 基于角色的权限控制
#[derive(Debug, Clone)]
pub struct Permission {
    pub resource: String,
    pub action: String,
    pub effect: Effect,
}

pub enum Effect {
    Allow,
    Deny,
}

// 权限检查
pub fn check_permission(
    user: &UserContext,
    permission: &Permission,
) -> Result<(), PermissionError> {
    // 权限检查逻辑
}
```

### 7.3 数据保护
- **敏感字段掩码**: 自动识别和掩码敏感字段
- **数据加密**: 配置信息加密存储
- **访问审计**: 记录所有数据访问日志
- **输入验证**: 严格的输入数据验证

## 8. 错误处理架构

### 8.1 错误类型设计
```rust
// 错误类型定义
#[derive(Debug, thiserror::Error)]
pub enum APIError {
    #[error("认证失败: {0}")]
    Unauthorized(String),
    
    #[error("权限不足: {0}")]
    Forbidden(String),
    
    #[error("资源不存在: {0}")]
    NotFound(String),
    
    #[error("验证失败: {0}")]
    Validation(String),
    
    #[error("内部服务错误: {0}")]
    Internal(String),
}

// 错误响应
#[derive(Debug, Serialize, Deserialize)]
pub struct ErrorResponse {
    pub error: String,
    pub status: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub details: Option<String>,
}
```

### 8.2 错误处理策略
- **统一错误处理**: 中间件统一处理错误
- **错误日志**: 详细错误日志记录
- **用户友好**: 用户友好的错误信息
- **调试信息**: 开发环境下详细错误信息

## 9. 部署架构

### 9.1 容器化部署
```dockerfile
# Dockerfile
FROM rust:1.75-slim AS builder
WORKDIR /app
COPY . .
RUN cargo build --release

FROM debian:bullseye-slim
COPY --from=builder /app/target/release/coco-server /usr/local/bin/
CMD ["coco-server"]
```

### 9.2 配置管理
```rust
// 配置结构体
#[derive(Debug, Deserialize)]
pub struct AppConfig {
    pub server: ServerConfig,
    pub database: DatabaseConfig,
    pub auth: AuthConfig,
    pub cache: CacheConfig,
}

// 环境变量支持
impl AppConfig {
    pub fn from_env() -> Result<Self, ConfigError> {
        // 从环境变量加载配置
    }
}
```

### 9.3 监控架构
- **健康检查**: `/health`端点
- **指标收集**: Prometheus格式指标
- **日志聚合**: 结构化JSON日志
- **分布式追踪**: OpenTelemetry支持

## 10. 测试架构

### 10.1 测试策略
- **单元测试**: 核心业务逻辑测试
- **集成测试**: API端点集成测试
- **端到端测试**: 完整业务流程测试
- **性能测试**: 性能基准测试

### 10.2 测试结构
```
tests/
├── unit/                     # 单元测试
│   ├── handlers/             # 处理器测试
│   ├── services/             # 服务测试
│   └── repositories/         # 仓库测试
├── integration/              # 集成测试
│   ├── api_tests.rs          # API测试
│   └── database_tests.rs     # 数据库测试
├── e2e/                      # 端到端测试
│   └── full_workflow_tests.rs # 完整流程测试
└── performance/              # 性能测试
    └── benchmark_tests.rs    # 基准测试
```

## 11. 扩展性考虑

### 11.1 水平扩展
- **无状态设计**: 应用层无状态化
- **数据分片**: Elasticsearch分片策略
- **负载均衡**: 支持多实例部署
- **缓存集群**: 分布式缓存支持

### 11.2 插件化架构
- **连接器插件**: 动态加载连接器
- **中间件插件**: 可插拔中间件
- **存储插件**: 支持多种存储后端
- **监控插件**: 可扩展监控能力

## 12. 总结

本架构设计充分考虑了性能、安全性、可维护性和扩展性要求，采用分层架构和模块化设计，确保与Go版本的完全兼容性。通过Rust语言的优势，提供更好的性能和内存安全保证，为后续的功能扩展和优化奠定坚实基础。