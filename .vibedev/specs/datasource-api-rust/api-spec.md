# DataSource API 规范文档

## 1. API概览

### 1.1 基本信息
- **API版本**: v1
- **基础路径**: `/datasource`
- **协议**: HTTP/1.1, HTTPS
- **数据格式**: JSON
- **字符编码**: UTF-8

### 1.2 通用规范
- **认证方式**: API Token (Bearer Token)
- **权限控制**: 基于角色的访问控制(RBAC)
- **错误格式**: 统一JSON错误响应
- **CORS支持**: 完整CORS支持
- **响应压缩**: 支持gzip压缩

## 2. API端点详细规范

### 2.1 创建数据源

#### 2.1.1 基本信息
- **端点**: `POST /datasource/`
- **描述**: 创建新的数据源
- **认证要求**: 必需
- **权限要求**: `datasource:create`

#### 2.1.2 请求规范

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {api_token}
```

**请求体**:
```json
{
  "name": "string (required, 1-255字符)",
  "type": "string (required, 枚举值: 'connector')",
  "description": "string (optional, 0-1000字符)",
  "icon": "string (optional)",
  "category": "string (optional)",
  "tags": ["string"] (optional, 数组最大长度50),
  "connector": {
    "id": "string (required, 连接器ID)",
    "config": "object (optional, 连接器配置)"
  },
  "sync_enabled": "boolean (optional, 默认: true)",
  "enabled": "boolean (optional, 默认: true)"
}
```

**字段验证规则**:
- `name`: 必需，长度1-255字符，只允许字母、数字、空格、下划线、中划线
- `type`: 必需，当前仅支持"connector"
- `connector.id`: 必需，必须是已存在的连接器ID
- `sync_enabled`: 可选，布尔值，默认true
- `enabled`: 可选，布尔值，默认true

#### 2.1.3 响应规范

**成功响应**:
```
状态码: 200 OK
```

```json
{
  "_id": "string (生成的数据源ID)",
  "result": "created"
}
```

**错误响应**:
```
状态码: 400 Bad Request
```

```json
{
  "error": "validation_failed",
  "status": "error",
  "details": {
    "field": "name",
    "message": "名称不能为空"
  }
}
```

**错误码表**:
| 状态码 | 错误类型 | 说明 |
|--------|----------|------|
| 400 | validation_failed | 请求验证失败 |
| 401 | unauthorized | 认证失败 |
| 403 | forbidden | 权限不足 |
| 500 | internal_error | 服务器内部错误 |

### 2.2 获取数据源

#### 2.2.1 基本信息
- **端点**: `GET /datasource/:id`
- **描述**: 获取指定ID的数据源详情
- **认证要求**: 必需
- **权限要求**: `datasource:read`

#### 2.2.2 请求规范

**路径参数**:
```
id: string (required, 数据源ID)
```

**请求头**:
```
Authorization: Bearer {api_token}
```

#### 2.2.3 响应规范

**成功响应 - 找到数据源**:
```
状态码: 200 OK
```

```json
{
  "found": true,
  "_id": "string (数据源ID)",
  "_source": {
    "id": "string",
    "type": "string",
    "name": "string",
    "description": "string | null",
    "icon": "string | null",
    "category": "string | null",
    "tags": ["string"] | null,
    "connector": {
      "id": "string",
      "config": "object | null"
    } | null,
    "sync_enabled": boolean,
    "enabled": boolean,
    "created": "string (ISO 8601格式)",
    "updated": "string (ISO 8601格式)"
  }
}
```

**成功响应 - 未找到数据源**:
```
状态码: 200 OK
```

```json
{
  "found": false,
  "_id": "string (请求的数据源ID)"
}
```

**敏感字段处理**:
- `connector.config` 字段在响应中会被掩码处理
- 实际响应中敏感配置会被替换为 `"[HIDDEN]"`

**错误响应**:
```
状态码: 401 Unauthorized
```

```json
{
  "error": "unauthorized",
  "status": "error",
  "details": "无效的API令牌"
}
```

### 2.3 更新数据源

#### 2.3.1 基本信息
- **端点**: `PUT /datasource/:id`
- **描述**: 更新指定ID的数据源
- **认证要求**: 必需
- **权限要求**: `datasource:update`

#### 2.3.2 请求规范

**路径参数**:
```
id: string (required, 数据源ID)
```

**查询参数**:
```
replace: boolean (optional, 默认: false)
- true: 忽略数据源不存在错误，创建新数据源
- false: 数据源不存在时返回错误
```

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {api_token}
```

**请求体**:
```json
{
  "name": "string (optional, 1-255字符)",
  "type": "string (optional, 枚举值: 'connector')",
  "description": "string (optional, 0-1000字符)",
  "icon": "string (optional)",
  "category": "string (optional)",
  "tags": ["string"] (optional, 数组最大长度50)",
  "connector": {
    "id": "string (optional, 连接器ID)",
    "config": "object (optional, 连接器配置)"
  },
  "sync_enabled": "boolean (optional)",
  "enabled": "boolean (optional)"
}
```

#### 2.3.3 响应规范

**成功响应**:
```
状态码: 200 OK
```

```json
{
  "_id": "string (数据源ID)",
  "result": "updated"
}
```

**错误响应 - 数据源不存在且replace=false**:
```
状态码: 404 Not Found
```

```json
{
  "_id": "string (请求的数据源ID)",
  "result": "not_found"
}
```

### 2.4 删除数据源

#### 2.4.1 基本信息
- **端点**: `DELETE /datasource/:id`
- **描述**: 删除指定ID的数据源及相关文档
- **认证要求**: 必需
- **权限要求**: `datasource:delete`

#### 2.4.2 请求规范

**路径参数**:
```
id: string (required, 数据源ID)
```

**请求头**:
```
Authorization: Bearer {api_token}
```

#### 2.4.3 响应规范

**成功响应**:
```
状态码: 200 OK
```

```json
{
  "_id": "string (数据源ID)",
  "result": "deleted"
}
```

**删除副作用**:
- 同时删除该数据源下的所有文档数据
- 清理相关的缓存数据
- 记录删除操作日志

**错误响应 - 数据源不存在**:
```
状态码: 404 Not Found
```

```json
{
  "_id": "string (请求的数据源ID)",
  "result": "not_found"
}
```

### 2.5 搜索数据源 (GET)

#### 2.5.1 基本信息
- **端点**: `GET /datasource/_search`
- **描述**: 使用查询参数搜索数据源
- **认证要求**: 必需
- **权限要求**: `datasource:search`

#### 2.5.2 请求规范

**查询参数**:
```
q: string (optional, 搜索关键词)
from: integer (optional, 默认: 0, 起始位置)
size: integer (optional, 默认: 10, 返回数量)
sort: string (optional, 排序字段)
order: string (optional, 排序方向: asc/desc)
filter: string (optional, 过滤条件)
```

**请求头**:
```
Authorization: Bearer {api_token}
```

#### 2.5.3 响应规范

**成功响应**:
```
状态码: 200 OK
```

```json
{
  "took": integer (搜索耗时, 毫秒),
  "timed_out": boolean (是否超时),
  "hits": {
    "total": {
      "value": integer (总命中数),
      "relation": "string (关系: eq/gte)"
    },
    "max_score": float (最高分数),
    "hits": [
      {
        "_index": "string (索引名)",
        "_type": "string (类型名)",
        "_id": "string (数据源ID)",
        "_score": float (匹配分数)",
        "_source": {
          "id": "string",
          "type": "string",
          "name": "string",
          "description": "string | null",
          "connector": {
            "id": "string",
            "config": "object | null"
          } | null,
          "sync_enabled": boolean,
          "enabled": boolean,
          "created": "string (ISO 8601格式)",
          "updated": "string (ISO 8601格式)"
        }
      }
    ]
  }
}
```

### 2.6 搜索数据源 (POST)

#### 2.6.1 基本信息
- **端点**: `POST /datasource/_search`
- **描述**: 使用JSON查询体搜索数据源
- **认证要求**: 必需
- **权限要求**: `datasource:search`

#### 2.6.2 请求规范

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {api_token}
```

**请求体**:
```json
{
  "query": {
    "match_all": {}
  },
  "from": 0,
  "size": 10,
  "sort": [
    {
      "created": "desc"
    }
  ],
  "aggs": {
    "category_stats": {
      "terms": {
        "field": "category"
      }
    }
  }
}
```

**支持的查询类型**:
- `match_all`: 匹配所有文档
- `match`: 全文匹配
- `term`: 精确匹配
- `terms`: 多值精确匹配
- `bool`: 布尔组合查询
- `range`: 范围查询

#### 2.6.3 响应规范

**成功响应**:
```
状态码: 200 OK
```

响应格式与GET搜索相同，支持聚合结果。

### 2.7 CORS预检请求

#### 2.7.1 基本信息
- **端点**: `OPTIONS /datasource/_search`
- **描述**: CORS预检请求
- **认证要求**: 可选

#### 2.7.2 请求规范

**请求头**:
```
Origin: string (请求源)
Access-Control-Request-Method: string (请求方法)
Access-Control-Request-Headers: string (请求头)
```

#### 2.7.3 响应规范

**成功响应**:
```
状态码: 200 OK
```

**响应头**:
```
Access-Control-Allow-Origin: *
Access-Control-Allow-Methods: GET, POST, OPTIONS
Access-Control-Allow-Headers: Content-Type, Authorization
Access-Control-Max-Age: 86400
```

### 2.8 创建文档

#### 2.8.1 基本信息
- **端点**: `POST /datasource/:id/_doc`
- **描述**: 在指定数据源中创建文档
- **认证要求**: 必需
- **权限要求**: `datasource:create`

#### 2.8.2 请求规范

**路径参数**:
```
id: string (required, 数据源ID)
```

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {api_token}
```

**请求体**:
```json
{
  "title": "string (optional)",
  "content": "string (optional)",
  "url": "string (optional)",
  "author": "string (optional)",
  "published_at": "string (optional, ISO 8601格式)",
  "tags": ["string"] (optional),
  "metadata": "object (optional)"
}
```

#### 2.8.3 响应规范

**成功响应**:
```
状态码: 200 OK
```

```json
{
  "_id": "string (生成的文档ID)",
  "result": "created"
}
```

**错误响应 - 数据源不存在**:
```
状态码: 404 Not Found
```

```json
{
  "error": "datasource_not_found",
  "status": "error",
  "details": "指定的数据源不存在"
}
```

### 2.9 创建指定ID文档

#### 2.9.1 基本信息
- **端点**: `POST /datasource/:id/_doc/:doc_id`
- **描述**: 在指定数据源中创建指定ID的文档
- **认证要求**: 必需
- **权限要求**: `datasource:create`

#### 2.9.2 请求规范

**路径参数**:
```
id: string (required, 数据源ID)
doc_id: string (required, 文档ID, 必须全局唯一)
```

**请求头**:
```
Content-Type: application/json
Authorization: Bearer {api_token}
```

**请求体**:
与创建文档相同

#### 2.9.3 响应规范

**成功响应**:
```
状态码: 200 OK
```

```json
{
  "_id": "string (指定的文档ID)",
  "result": "created"
}
```

**错误响应 - 文档ID已存在**:
```
状态码: 409 Conflict
```

```json
{
  "error": "document_already_exists",
  "status": "error",
  "details": "指定ID的文档已存在"
}
```

## 3. 通用响应规范

### 3.1 成功响应格式
```json
{
  "success": boolean,
  "data": object,
  "timestamp": string (ISO 8601格式),
  "request_id": string (请求跟踪ID)
}
```

### 3.2 错误响应格式
```json
{
  "error": string (错误类型),
  "message": string (错误消息),
  "status": string (状态标识),
  "details": object (错误详情),
  "timestamp": string (ISO 8601格式),
  "request_id": string (请求跟踪ID)
}
```

### 3.3 分页响应格式
```json
{
  "data": [],
  "pagination": {
    "page": integer,
    "size": integer,
    "total": integer,
    "pages": integer
  }
}
```

## 4. 数据类型规范

### 4.1 基本数据类型
- **string**: UTF-8字符串
- **integer**: 64位整数
- **float**: 64位浮点数
- **boolean**: 布尔值
- **array**: JSON数组
- **object**: JSON对象
- **datetime**: ISO 8601格式时间字符串

### 4.2 枚举类型
```json
{
  "DataSourceType": {
    "description": "数据源类型",
    "values": ["connector"]
  },
  "SortOrder": {
    "description": "排序方向",
    "values": ["asc", "desc"]
  }
}
```

### 4.3 状态码规范
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 201 | 资源创建成功 |
| 400 | 请求参数错误 |
| 401 | 认证失败 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 422 | 数据验证失败 |
| 500 | 服务器内部错误 |
| 503 | 服务暂时不可用 |

## 5. 安全规范

### 5.1 认证规范
- 使用Bearer Token认证
- Token在请求头的Authorization字段中传递
- Token格式: `Bearer {api_token}`
- Token有效期: 根据系统配置决定

### 5.2 权限规范
- 基于角色的访问控制(RBAC)
- 权限格式: `{resource}:{action}`
- 支持的资源: datasource, document
- 支持的操作: create, read, update, delete, search

### 5.3 数据保护规范
- 敏感字段自动掩码
- 输入数据严格验证
- 输出数据过滤处理
- 访问日志记录

## 6. 性能规范

### 6.1 响应时间要求
- CRUD操作: 平均响应时间 < 50ms
- 搜索操作: 平均响应时间 < 100ms
- 批量操作: 响应时间线性增长
- 并发请求: 支持1000+ QPS

### 6.2 分页规范
- 默认页面大小: 10
- 最大页面大小: 100
- 支持游标分页
- 支持基于偏移的分页

### 6.3 缓存规范
- 热点数据缓存
- 缓存过期时间: 30分钟
- 支持缓存手动刷新
- 缓存命中率监控

## 7. 兼容性规范

### 7.1 向后兼容性
- API端点保持不变
- 响应格式向下兼容
- 新增字段为可选字段
- 废弃字段提供迁移期

### 7.2 版本控制
- 使用URL路径版本控制
- 支持多版本并行运行
- 版本切换平滑过渡
- 废弃版本提前通知

### 7.3 数据格式兼容
- JSON格式标准兼容
- 字符编码UTF-8兼容
- 时间格式ISO 8601兼容
- 数值类型JSON标准兼容

## 8. 监控规范

### 8.1 指标收集
- 请求成功率
- 响应时间分布
- 错误率统计
- 并发连接数

### 8.2 日志规范
- 结构化JSON日志
- 请求追踪ID
- 错误详细记录
- 性能指标记录

### 8.3 健康检查
- `/health`端点
- 系统状态检查
- 依赖服务检查
- 资源使用检查

## 9. 测试规范

### 9.1 接口测试
- 所有API端点测试覆盖
- 正常流程测试
- 异常流程测试
- 边界条件测试

### 9.2 性能测试
- 负载测试
- 压力测试
- 稳定性测试
- 内存泄漏测试

### 9.3 兼容性测试
- 与Go版本对比测试
- 客户端兼容性测试
- 数据迁移测试
- 版本升级测试

## 10. 附录

### 10.1 示例请求
详细的API调用示例，包括各种场景的完整请求和响应。

### 10.2 错误代码表
完整的错误代码列表及其解决方案。

### 10.3 数据迁移指南
从Go版本迁移到Rust版本的详细步骤和注意事项。

### 10.4 最佳实践
API使用的最佳实践建议和性能优化建议。