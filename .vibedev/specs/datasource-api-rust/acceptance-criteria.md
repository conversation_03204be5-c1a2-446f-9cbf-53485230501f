# DataSource API 验收标准

## 1. 功能性验收标准

### 1.1 CRUD操作验收标准

#### 1.1.1 创建数据源 (POST /datasource/)
**验收条件**：
- ✅ 能够成功创建connector类型的数据源
- ✅ 支持所有必需字段：name, type, connector
- ✅ 支持可选字段：description, category, tags, icon
- ✅ 验证connector.id的有效性
- ✅ 自动生成数据源ID
- ✅ 返回格式：`{"_id": "generated_id", "result": "created"}`
- ✅ HTTP状态码：200（成功创建）
- ✅ 数据持久化到数据库
- ✅ 创建后自动清理相关缓存

**验收测试方法**：
```bash
# 测试创建基本数据源
curl -X POST http://localhost:9000/datasource \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test DataSource",
    "type": "connector",
    "connector": {
      "id": "hugo_site",
      "config": {
        "urls": ["https://example.com/index.json"]
      }
    }
  }'

# 验证响应格式
# 预期：HTTP 200, 响应包含"_id"和"result": "created"
```

#### 1.1.2 获取数据源 (GET /datasource/:id)
**验收条件**：
- ✅ 能够根据ID获取现有数据源
- ✅ 返回格式：`{"found": true, "_id": "id", "_source": {...}}`
- ✅ 数据源不存在时返回：`{"found": false, "_id": "id"}`
- ✅ HTTP状态码：200（找到）或404（未找到）
- ✅ 敏感字段（config）正确掩码处理
- ✅ 返回完整的元数据信息

**验收测试方法**：
```bash
# 测试获取存在的数据源
curl -X GET http://localhost:9000/datasource/{existing_id}

# 验证响应格式
# 预期：HTTP 200, found: true, 包含_source字段

# 测试获取不存在的数据源
curl -X GET http://localhost:9000/datasource/nonexistent_id

# 验证响应格式
# 预期：HTTP 200, found: false
```

#### 1.1.3 更新数据源 (PUT /datasource/:id)
**验收条件**：
- ✅ 能够更新现有数据源的配置
- ✅ 支持replace参数控制更新策略
- ✅ 保持原有created时间不变
- ✅ 更新后自动清理相关缓存
- ✅ 返回格式：`{"_id": "id", "result": "updated"}`
- ✅ HTTP状态码：200（成功更新）
- ✅ 数据源不存在时返回：`{"_id": "id", "result": "not_found"}`

**验收测试方法**：
```bash
# 测试更新数据源
curl -X PUT http://localhost:9000/datasource/{existing_id} \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Updated DataSource",
    "description": "Updated description"
  }'

# 验证响应格式
# 预期：HTTP 200, result: "updated"
```

#### 1.1.4 删除数据源 (DELETE /datasource/:id)
**验收条件**：
- ✅ 能够删除指定ID的数据源
- ✅ 同时删除相关的文档数据
- ✅ 删除后自动清理相关缓存
- ✅ 返回格式：`{"_id": "id", "result": "deleted"}`
- ✅ HTTP状态码：200（成功删除）
- ✅ 数据源不存在时返回：`{"_id": "id", "result": "not_found"}`

**验收测试方法**：
```bash
# 测试删除数据源
curl -X DELETE http://localhost:9000/datasource/{existing_id}

# 验证响应格式
# 预期：HTTP 200, result: "deleted"

# 验证相关文档也被删除
```

### 1.2 搜索操作验收标准

#### 1.2.1 GET搜索 (GET /datasource/_search)
**验收条件**：
- ✅ 支持URL查询参数搜索
- ✅ 支持分页参数：from, size
- ✅ 支持排序参数
- ✅ 支持过滤条件
- ✅ 返回标准Elasticsearch搜索格式
- ✅ 支持Integration ID过滤
- ✅ CORS头部正确设置
- ✅ HTTP状态码：200

**验收测试方法**：
```bash
# 测试基本搜索
curl -X GET "http://localhost:9000/datasource/_search"

# 测试带查询参数的搜索
curl -X GET "http://localhost:9000/datasource/_search?q=name:test&size=10&from=0"

# 验证响应格式
# 预期：HTTP 200, 包含took, timed_out, hits等字段
```

#### 1.2.2 POST搜索 (POST /datasource/_search)
**验收条件**：
- ✅ 支持JSON查询体
- ✅ 支持复杂Elasticsearch查询语法
- ✅ 支持聚合查询
- ✅ 支持Integration ID过滤
- ✅ 敏感字段自动过滤
- ✅ CORS头部正确设置
- ✅ HTTP状态码：200

**验收测试方法**：
```bash
# 测试JSON查询
curl -X POST http://localhost:9000/datasource/_search \
  -H "Content-Type: application/json" \
  -d '{
    "query": {
      "match": {
        "name": "test"
      }
    },
    "size": 10
  }'

# 验证响应格式
# 预期：HTTP 200, 包含搜索结果
```

#### 1.2.3 OPTIONS搜索 (OPTIONS /datasource/_search)
**验收条件**：
- ✅ 正确处理CORS预检请求
- ✅ 返回适当的CORS头部
- ✅ HTTP状态码：200

**验收测试方法**：
```bash
# 测试CORS预检
curl -X OPTIONS http://localhost:9000/datasource/_search \
  -H "Origin: http://localhost:3000" \
  -H "Access-Control-Request-Method: POST" \
  -H "Access-Control-Request-Headers: Content-Type"

# 验证CORS头部
# 预期：HTTP 200, 包含适当的CORS头部
```

### 1.3 文档管理验收标准

#### 1.3.1 创建文档 (POST /datasource/:id/_doc)
**验收条件**：
- ✅ 能够在指定数据源中创建文档
- ✅ 自动生成文档ID
- ✅ 自动设置文档的source引用
- ✅ 验证数据源的有效性
- ✅ 返回格式：`{"_id": "doc_id", "result": "created"}`
- ✅ HTTP状态码：200（成功创建）

**验收测试方法**：
```bash
# 测试创建文档
curl -X POST http://localhost:9000/datasource/{datasource_id}/_doc \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Document",
    "content": "This is a test document"
  }'

# 验证响应格式
# 预期：HTTP 200, result: "created"
```

#### 1.3.2 创建指定ID文档 (POST /datasource/:id/_doc/:doc_id)
**验收条件**：
- ✅ 能够在指定数据源中创建指定ID的文档
- ✅ 使用指定的文档ID
- ✅ 验证文档ID的唯一性
- ✅ 自动设置文档的source引用
- ✅ 返回格式：`{"_id": "doc_id", "result": "created"}`
- ✅ HTTP状态码：200（成功创建）

**验收测试方法**：
```bash
# 测试创建指定ID文档
curl -X POST http://localhost:9000/datasource/{datasource_id}/_doc/custom_doc_id \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Test Document",
    "content": "This is a test document"
  }'

# 验证响应格式
# 预期：HTTP 200, result: "created"
```

## 2. 数据模型验收标准

### 2.1 DataSource结构体验收
**验收条件**：
- ✅ 包含所有必需字段：id, type, name, connector, sync_enabled, enabled
- ✅ 支持所有可选字段：description, icon, category, tags
- ✅ connector字段包含：id, config
- ✅ 时间戳字段：created, updated
- ✅ 字段类型正确
- ✅ JSON序列化/反序列化正常

### 2.2 数据验证验收标准
**验收条件**：
- ✅ name字段非空验证
- ✅ type字段枚举值验证
- ✅ connector.id字段有效性验证
- ✅ 布尔字段类型验证
- ✅ 字符串字段长度验证
- ✅ 敏感字段格式验证

## 3. 错误处理验收标准

### 3.1 HTTP状态码验收
**验收条件**：
- ✅ 200：操作成功
- ✅ 400：请求格式错误
- ✅ 401：认证失败
- ✅ 403：权限不足
- ✅ 404：资源不存在
- ✅ 500：服务器内部错误

### 3.2 错误响应格式验收
**验收条件**：
- ✅ 错误响应包含error字段
- ✅ 错误响应包含status字段
- ✅ 错误信息清晰明确
- ✅ 错误格式一致
- ✅ 支持中文错误信息

### 3.3 异常处理验收标准
**验收条件**：
- ✅ 数据库连接异常处理
- ✅ 数据格式异常处理
- ✅ 认证异常处理
- ✅ 权限异常处理
- ✅ 网络异常处理
- ✅ 资源不存在异常处理

## 4. 性能验收标准

### 4.1 响应时间验收
**验收条件**：
- ✅ CRUD操作平均响应时间 < 50ms
- ✅ 搜索操作平均响应时间 < 100ms
- ✅ 并发1000 QPS时响应时间稳定
- ✅ 冷启动时间 < 5秒

### 4.2 资源使用验收
**验收条件**：
- ✅ 内存使用合理，无内存泄漏
- ✅ CPU使用率在正常范围内
- ✅ 数据库连接池配置合理
- ✅ 文件描述符使用合理

### 4.3 并发处理验收
**验收条件**：
- ✅ 支持1000+并发连接
- ✅ 并发情况下数据一致性
- ✅ 并发情况下性能稳定
- ✅ 无竞态条件

## 5. 安全性验收标准

### 5.1 认证验收
**验收条件**：
- ✅ 支持API Token认证
- ✅ Token验证机制正确
- ✅ 认证失败返回401状态码
- ✅ 认证信息不泄露

### 5.2 授权验收
**验收条件**：
- ✅ 基于角色的权限控制
- ✅ 细粒度操作权限
- ✅ 权限验证失败返回403状态码
- ✅ 权限配置动态生效

### 5.3 数据保护验收
**验收条件**：
- ✅ 敏感字段自动掩码
- ✅ 配置信息加密存储
- ✅ 访问日志记录
- ✅ SQL注入防护

## 6. 兼容性验收标准

### 6.1 API兼容性验收
**验收条件**：
- ✅ 与Go版本API端点完全一致
- ✅ 请求/响应格式完全兼容
- ✅ HTTP状态码一致
- ✅ 错误处理机制一致

### 6.2 数据兼容性验收
**验收条件**：
- ✅ 支持Go版本创建的数据
- ✅ 数据结构完全兼容
- ✅ 迁移脚本可用
- ✅ 双版本并行运行

### 6.3 客户端兼容性验收
**验收条件**：
- ✅ 现有客户端无需修改
- ✅ Web界面正常工作
- ✅ API调用脚本正常
- ✅ 第三方集成正常

## 7. 可维护性验收标准

### 7.1 代码质量验收
**验收条件**：
- ✅ 代码遵循Rust最佳实践
- ✅ 变量命名使用驼峰式
- ✅ 代码注释使用中文
- ✅ 错误处理完善
- ✅ 日志记录完整

### 7.2 文档验收
**验收条件**：
- ✅ API文档完整
- ✅ 代码注释清晰
- ✅ 部署文档详细
- ✅ 示例代码可用

### 7.3 测试覆盖验收
**验收条件**：
- ✅ 单元测试覆盖率 > 80%
- ✅ 集成测试完整
- ✅ 端到端测试通过
- ✅ 性能测试通过

## 8. 部署验收标准

### 8.1 容器化部署验收
**验收条件**：
- ✅ 支持Docker容器化
- ✅ Dockerfile配置正确
- ✅ 环境变量配置支持
- ✅ 健康检查端点可用

### 8.2 配置管理验收
**验收条件**：
- ✅ 配置文件格式正确
- ✅ 环境变量覆盖机制
- ✅ 配置验证机制
- ✅ 配置热更新支持

### 8.3 监控验收
**验收条件**：
- ✅ 健康检查端点
- ✅ 指标监控接口
- ✅ 日志输出规范
- ✅ 错误追踪机制

## 9. 验收流程

### 9.1 功能验收流程
1. **单元测试**：所有核心功能单元测试通过
2. **集成测试**：API端点集成测试通过
3. **端到端测试**：完整业务流程测试通过
4. **兼容性测试**：与Go版本兼容性测试通过

### 9.2 性能验收流程
1. **基准测试**：单个API性能基准测试
2. **负载测试**：并发负载测试
3. **稳定性测试**：长时间运行稳定性测试
4. **资源监控**：内存和CPU使用监控

### 9.3 安全验收流程
1. **安全扫描**：代码安全扫描
2. **渗透测试**：API安全渗透测试
3. **权限测试**：权限控制测试
4. **数据保护测试**：敏感数据保护测试

## 10. 验收标准检查清单

### 10.1 核心功能检查清单
- [ ] 所有CRUD API端点实现正确
- [ ] 搜索功能完整实现
- [ ] 文档管理功能正常
- [ ] 数据模型结构正确
- [ ] 错误处理机制完善

### 10.2 性能检查清单
- [ ] 响应时间符合要求
- [ ] 并发处理能力达标
- [ ] 资源使用合理
- [ ] 内存无泄漏

### 10.3 兼容性检查清单
- [ ] 与Go版本API完全兼容
- [ ] 客户端无需修改
- [ ] 数据迁移支持
- [ ] 配置文件兼容

### 10.4 安全性检查清单
- [ ] 认证机制正确
- [ ] 权限控制完善
- [ ] 数据保护措施到位
- [ ] 访问日志完整

### 10.5 可维护性检查清单
- [ ] 代码质量达标
- [ ] 文档完整准确
- [ ] 测试覆盖率达标
- [ ] 部署流程清晰

## 11. 验收结论

### 11.1 验收通过条件
所有验收标准检查清单项目均为✅通过状态，方可认为DataSource API Rust重写项目验收通过。

### 11.2 验收不通过处理
如存在不通过的验收项目，需要：
1. 分析不通过原因
2. 制定修复计划
3. 重新测试验证
4. 更新验收状态

### 11.3 验收报告
验收完成后需要生成详细的验收报告，包括：
- 验收过程概述
- 验收结果统计
- 发现的问题和解决方案
- 性能测试结果
- 安全测试结果
- 最终验收结论