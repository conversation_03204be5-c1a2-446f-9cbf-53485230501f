# 模型提供商API系统架构设计

## 架构概述

本文档定义了模型提供商API的系统架构，采用分层架构模式，确保与Go版本API的完全兼容性，同时利用SurrealDB的优势提供高性能的数据访问。

## 系统架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    客户端层 (Client Layer)                    │
├─────────────────────────────────────────────────────────────┤
│  coco-app (Tauri)  │  Web Browser  │  API Clients  │  CLI   │
└─────────────────────────────────────────────────────────────┘
                                │
                         HTTP/HTTPS
                                │
┌─────────────────────────────────────────────────────────────┐
│                   API网关层 (API Gateway)                    │
├─────────────────────────────────────────────────────────────┤
│              Axum Router + CORS + Middleware               │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  处理层 (Handler Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  ModelProviderHandler  │  ResponseFormatter  │  ErrorHandler │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  服务层 (Service Layer)                     │
├─────────────────────────────────────────────────────────────┤
│  ModelProviderService  │  ValidationService  │  CacheManager │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                 仓储层 (Repository Layer)                   │
├─────────────────────────────────────────────────────────────┤
│  ModelProviderRepository  │  QueryBuilder  │  DataMapper    │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                  数据层 (Data Layer)                        │
├─────────────────────────────────────────────────────────────┤
│                        SurrealDB                           │
└─────────────────────────────────────────────────────────────┘
```

## 分层架构详细设计

### 1. API网关层 (API Gateway)

**职责**：

- HTTP请求路由
- CORS处理
- 中间件管理
- 请求/响应日志

**技术实现**：

```rust
// 路由配置
Router::new()
    .route("/model_provider/", post(create_handler))
    .route("/model_provider/:id", get(get_handler))
    .route("/model_provider/:id", put(update_handler))
    .route("/model_provider/:id", delete(delete_handler))
    .route("/model_provider/_search", get(search_get_handler))
    .route("/model_provider/_search", post(search_post_handler))
    .route("/model_provider/_search", options(search_options_handler))
    .layer(cors_layer)
    .layer(logging_middleware)
```

### 2. 处理层 (Handler Layer)

**职责**：

- HTTP请求解析
- 参数验证
- 响应格式转换
- 错误处理

**核心组件**：

#### ModelProviderHandler

```rust
pub struct ModelProviderHandler {
    service: Arc<ModelProviderService>,
    formatter: Arc<ResponseFormatter>,
}

impl ModelProviderHandler {
    pub async fn create(&self, req: CreateRequest) -> Result<Response, ApiError>
    pub async fn get(&self, id: String) -> Result<Response, ApiError>
    pub async fn update(&self, id: String, req: UpdateRequest) -> Result<Response, ApiError>
    pub async fn delete(&self, id: String) -> Result<Response, ApiError>
    pub async fn search(&self, params: SearchParams) -> Result<Response, ApiError>
}
```

#### ResponseFormatter

```rust
pub struct ResponseFormatter;

impl ResponseFormatter {
    // 转换为Go版本兼容的响应格式
    pub fn format_create_response(id: &str) -> serde_json::Value
    pub fn format_get_response(id: &str, data: &ModelProvider) -> serde_json::Value
    pub fn format_update_response(id: &str) -> serde_json::Value
    pub fn format_delete_response(id: &str) -> serde_json::Value
    pub fn format_search_response(results: &SearchResult) -> serde_json::Value
}
```

### 3. 服务层 (Service Layer)

**职责**：

- 业务逻辑实现
- 数据验证
- 缓存管理
- 事务协调

**核心组件**：

#### ModelProviderService

```rust
pub struct ModelProviderService {
    repository: Arc<dyn ModelProviderRepository>,
    cache: Arc<CacheManager>,
    validator: Arc<ValidationService>,
}

impl ModelProviderService {
    pub async fn create(&self, req: CreateModelProviderRequest) -> Result<String, ServiceError>
    pub async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, ServiceError>
    pub async fn update(&self, id: &str, req: UpdateModelProviderRequest) -> Result<(), ServiceError>
    pub async fn delete(&self, id: &str) -> Result<(), ServiceError>
    pub async fn search(&self, params: SearchParams) -> Result<SearchResult, ServiceError>

    // 业务逻辑方法
    async fn validate_builtin_protection(&self, provider: &ModelProvider) -> Result<(), ServiceError>
    async fn check_name_uniqueness(&self, name: &str, exclude_id: Option<&str>) -> Result<(), ServiceError>
    async fn invalidate_cache(&self, id: &str)
}
```

#### CacheManager

```rust
pub struct CacheManager {
    cache: Arc<RwLock<HashMap<String, CacheEntry>>>,
}

pub struct CacheEntry {
    data: ModelProvider,
    expires_at: DateTime<Utc>,
}

impl CacheManager {
    pub async fn get(&self, key: &str) -> Option<ModelProvider>
    pub async fn set(&self, key: &str, value: ModelProvider, ttl: Duration)
    pub async fn invalidate(&self, key: &str)
    pub async fn cleanup_expired(&self) // 定期清理过期缓存
}
```

### 4. 仓储层 (Repository Layer)

**职责**：

- 数据访问抽象
- 查询构建
- 数据映射
- 事务管理

**核心组件**：

#### ModelProviderRepository (Trait)

```rust
#[async_trait]
pub trait ModelProviderRepository: Send + Sync {
    async fn create(&self, provider: &ModelProvider) -> Result<String, RepositoryError>;
    async fn get_by_id(&self, id: &str) -> Result<Option<ModelProvider>, RepositoryError>;
    async fn update(&self, provider: &ModelProvider) -> Result<(), RepositoryError>;
    async fn delete(&self, id: &str) -> Result<(), RepositoryError>;
    async fn search(&self, query: &SearchQuery) -> Result<SearchResult, RepositoryError>;
    async fn find_by_name(&self, name: &str) -> Result<Option<ModelProvider>, RepositoryError>;
    async fn exists(&self, id: &str) -> Result<bool, RepositoryError>;
}
```

#### SurrealModelProviderRepository

```rust
pub struct SurrealModelProviderRepository {
    db: Arc<Surreal<Client>>,
    query_builder: Arc<QueryBuilder>,
}

impl ModelProviderRepository for SurrealModelProviderRepository {
    // 实现所有trait方法
}
```

#### QueryBuilder

```rust
pub struct QueryBuilder;

impl QueryBuilder {
    pub fn build_search_query(&self, params: &SearchParams) -> String {
        // 构建SurrealDB查询语句
        // 支持：分页、排序、基础过滤、简单文本搜索
    }

    pub fn build_filter_conditions(&self, filters: &HashMap<String, Value>) -> String
    pub fn build_sort_clause(&self, sort: &str) -> String
    pub fn build_pagination_clause(&self, size: usize, from: usize) -> String
}
```

## 数据模型设计

### 核心数据结构

```rust
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelProvider {
    pub id: String,
    pub created: DateTime<Utc>,
    pub updated: DateTime<Utc>,
    pub name: String,
    pub api_key: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<ModelConfig>,
    pub enabled: bool,
    pub builtin: bool,
    pub description: String,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelConfig {
    pub name: String,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub settings: Option<ModelSettings>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ModelSettings {
    #[serde(skip_serializing_if = "Option::is_none")]
    pub temperature: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub top_p: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub presence_penalty: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub frequency_penalty: Option<f64>,
    #[serde(skip_serializing_if = "Option::is_none")]
    pub max_tokens: Option<u32>,
}
```

### 搜索相关结构

```rust
#[derive(Debug, Deserialize)]
pub struct SearchParams {
    #[serde(default = "default_size")]
    pub size: usize,
    #[serde(default)]
    pub from: usize,
    #[serde(default)]
    pub sort: String,
    #[serde(default)]
    pub q: String, // 简单文本搜索
    #[serde(flatten)]
    pub filters: HashMap<String, serde_json::Value>,
}

#[derive(Debug, Serialize)]
pub struct SearchResult {
    pub hits: Vec<ModelProvider>,
    pub total: usize,
    pub took: u64, // 查询耗时(毫秒)
}
```

## 错误处理架构

### 错误类型层次

```rust
#[derive(Debug, thiserror::Error)]
pub enum ApiError {
    #[error("Service error: {0}")]
    Service(#[from] ServiceError),
    #[error("Validation error: {0}")]
    Validation(String),
    #[error("Not found: {0}")]
    NotFound(String),
}

#[derive(Debug, thiserror::Error)]
pub enum ServiceError {
    #[error("Repository error: {0}")]
    Repository(#[from] RepositoryError),
    #[error("Business logic error: {0}")]
    BusinessLogic(String),
    #[error("Cache error: {0}")]
    Cache(String),
}

#[derive(Debug, thiserror::Error)]
pub enum RepositoryError {
    #[error("Database error: {0}")]
    Database(String),
    #[error("Query error: {0}")]
    Query(String),
    #[error("Serialization error: {0}")]
    Serialization(String),
}
```

## 性能优化策略

### 1. 缓存策略

- **L1缓存**: 内存HashMap，30分钟TTL
- **缓存键**: `model_provider:{id}`
- **缓存失效**: 创建/更新/删除时自动清除
- **缓存预热**: 启动时加载热点数据

### 2. 数据库优化

- **索引策略**: name字段唯一索引，enabled字段普通索引
- **查询优化**: 使用SurrealDB的原生查询能力
- **连接池**: 配置合适的连接池大小

### 3. 并发处理

- **异步处理**: 全链路异步，避免阻塞
- **读写分离**: 读操作优先使用缓存
- **批量操作**: 支持批量查询优化

## 安全考虑

### 1. 数据保护

- **敏感字段过滤**: 响应中自动过滤api_key等敏感信息
- **输入验证**: 严格的参数验证和清理
- **SQL注入防护**: 使用参数化查询

### 2. 访问控制

- **当前阶段**: 暂不实现权限控制
- **预留接口**: 为后续权限集成预留扩展点
- **审计日志**: 记录所有操作日志

## 部署架构

### 1. 服务部署

- **容器化**: Docker容器部署
- **负载均衡**: 支持多实例部署
- **健康检查**: 提供健康检查端点

### 2. 数据库部署

- **SurrealDB**: 独立部署或嵌入式模式
- **备份策略**: 定期数据备份
- **监控**: 数据库性能监控

### 3. 监控和日志

- **应用监控**: 性能指标收集
- **日志聚合**: 结构化日志输出
- **告警机制**: 关键指标告警

## 系统初始化架构

### 1. 配置管理系统

**配置文件结构**:

```
coco-server-rust/
├── config/
│   ├── model_provider.toml    # 内置提供商配置
│   ├── app.toml              # 应用配置
│   └── database.toml         # 数据库配置
```

**配置加载流程**:

```rust
pub struct ConfigManager {
    app_config: AppConfig,
    model_providers: Vec<BuiltinProvider>,
    config_version: String,
}

impl ConfigManager {
    pub async fn load() -> Result<Self, ConfigError> {
        // 1. 加载应用配置
        let app_config = Self::load_app_config().await?;

        // 2. 加载内置提供商配置
        let model_providers = Self::load_builtin_providers().await?;

        // 3. 验证配置完整性
        Self::validate_config(&app_config, &model_providers)?;

        Ok(ConfigManager {
            app_config,
            model_providers,
            config_version: Self::get_config_version(),
        })
    }

    pub async fn reload_providers(&mut self) -> Result<(), ConfigError> {
        // 热重载内置提供商配置
    }
}
```

### 2. 初始化服务架构

**InitializationService**:

```rust
pub struct InitializationService {
    repository: Arc<dyn ModelProviderRepository>,
    config_manager: Arc<ConfigManager>,
}

impl InitializationService {
    pub async fn initialize_system(&self) -> Result<(), InitError> {
        // 1. 检查数据库连接
        self.check_database_connection().await?;

        // 2. 创建必要的表结构
        self.ensure_database_schema().await?;

        // 3. 导入内置提供商
        self.import_builtin_providers().await?;

        // 4. 验证系统状态
        self.validate_system_state().await?;

        Ok(())
    }

    async fn import_builtin_providers(&self) -> Result<(), InitError> {
        let providers = self.config_manager.get_builtin_providers();

        for provider_config in providers {
            match self.process_builtin_provider(provider_config).await {
                Ok(_) => tracing::info!("导入内置提供商成功: {}", provider_config.id),
                Err(e) => tracing::warn!("导入内置提供商失败: {} - {}", provider_config.id, e),
            }
        }

        Ok(())
    }

    async fn process_builtin_provider(&self, config: &BuiltinProviderConfig) -> Result<(), InitError> {
        // 检查是否已存在
        if let Some(existing) = self.repository.get_by_id(&config.id).await? {
            // 更新策略：保留用户修改的敏感字段
            self.update_existing_builtin(existing, config).await?;
        } else {
            // 创建新的内置提供商
            self.create_new_builtin(config).await?;
        }

        Ok(())
    }
}
```

### 3. 配置热重载系统

**ConfigReloadService**:

```rust
pub struct ConfigReloadService {
    config_manager: Arc<RwLock<ConfigManager>>,
    init_service: Arc<InitializationService>,
}

impl ConfigReloadService {
    pub async fn reload_config(&self) -> Result<ReloadResult, ReloadError> {
        // 1. 备份当前配置
        let backup = self.backup_current_config().await;

        // 2. 尝试加载新配置
        match ConfigManager::load().await {
            Ok(new_config) => {
                // 3. 验证新配置
                if self.validate_new_config(&new_config).await? {
                    // 4. 应用新配置
                    self.apply_new_config(new_config).await?;
                    Ok(ReloadResult::Success)
                } else {
                    Ok(ReloadResult::ValidationFailed)
                }
            }
            Err(e) => {
                // 5. 恢复备份配置
                self.restore_backup(backup).await?;
                Err(ReloadError::LoadFailed(e))
            }
        }
    }

    pub async fn watch_config_files(&self) -> Result<(), WatchError> {
        // 实现文件监控，自动触发重载
        use notify::{Watcher, RecursiveMode, watcher};

        let (tx, rx) = std::sync::mpsc::channel();
        let mut watcher = watcher(tx, Duration::from_secs(2))?;

        watcher.watch("config/", RecursiveMode::Recursive)?;

        // 处理文件变更事件
        loop {
            match rx.recv() {
                Ok(event) => {
                    if self.should_reload(&event) {
                        if let Err(e) = self.reload_config().await {
                            tracing::error!("配置重载失败: {}", e);
                        }
                    }
                }
                Err(e) => {
                    tracing::error!("文件监控错误: {}", e);
                    break;
                }
            }
        }

        Ok(())
    }
}
```

### 4. 数据模型扩展

**内置提供商配置结构**:

```rust
#[derive(Debug, Clone, Deserialize)]
pub struct BuiltinProviderConfig {
    pub id: String,
    pub name: String,
    pub api_key: String,
    pub api_type: String,
    pub base_url: String,
    pub icon: String,
    pub models: Vec<ModelConfig>,
    pub enabled: bool,
    pub description: String,
}

#[derive(Debug, Clone, Deserialize)]
pub struct ConfigMetadata {
    pub version: String,
    pub description: String,
    pub last_updated: DateTime<Utc>,
}

#[derive(Debug, Clone, Deserialize)]
pub struct ModelProviderConfig {
    pub metadata: ConfigMetadata,
    pub providers: Vec<BuiltinProviderConfig>,
}
```

### 5. 初始化流程图

```
系统启动
    ↓
加载配置文件
    ↓
验证配置格式
    ↓
连接数据库
    ↓
检查表结构
    ↓
读取现有提供商
    ↓
对比配置差异
    ↓
更新/创建提供商
    ↓
记录操作日志
    ↓
启动配置监控
    ↓
系统就绪
```

### 6. 错误处理和恢复

**初始化错误类型**:

```rust
#[derive(Debug, thiserror::Error)]
pub enum InitError {
    #[error("配置文件错误: {0}")]
    ConfigError(String),

    #[error("数据库连接失败: {0}")]
    DatabaseError(String),

    #[error("提供商导入失败: {0}")]
    ProviderImportError(String),

    #[error("系统验证失败: {0}")]
    ValidationError(String),
}

impl InitializationService {
    async fn handle_init_error(&self, error: InitError) -> Result<(), InitError> {
        match error {
            InitError::ConfigError(_) => {
                // 使用默认配置继续启动
                tracing::warn!("使用默认配置启动系统");
                self.use_default_config().await
            }
            InitError::DatabaseError(_) => {
                // 重试数据库连接
                self.retry_database_connection().await
            }
            InitError::ProviderImportError(_) => {
                // 跳过失败的提供商，继续导入其他
                tracing::warn!("跳过失败的提供商导入");
                Ok(())
            }
            InitError::ValidationError(_) => {
                // 系统验证失败，停止启动
                Err(error)
            }
        }
    }
}
```

### 7. 管理API端点

**配置管理API**:

```rust
// 重载配置
POST /admin/config/reload

// 获取配置状态
GET /admin/config/status

// 验证配置文件
POST /admin/config/validate

// 获取内置提供商列表
GET /admin/builtin-providers

// 重置内置提供商
POST /admin/builtin-providers/reset
```
