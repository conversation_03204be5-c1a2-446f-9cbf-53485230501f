# 模型提供商API测试计划

## 测试策略概述

本文档定义了模型提供商API的全面测试策略，确保功能正确性、性能达标和与Go版本的完全兼容性。

## 测试金字塔

```
        E2E Tests (5%)
       ┌─────────────────┐
      │  端到端测试      │
     └─────────────────┘
    
    Integration Tests (25%)
   ┌─────────────────────────┐
  │     集成测试             │
 └─────────────────────────┘

Unit Tests (70%)
┌─────────────────────────────────┐
│          单元测试                │
└─────────────────────────────────┘
```

## 测试分类和覆盖率目标

| 测试类型 | 覆盖率目标 | 执行频率 | 工具 |
|----------|------------|----------|------|
| 单元测试 | >80% | 每次提交 | cargo test |
| 集成测试 | >90% API覆盖 | 每次构建 | cargo test --test |
| 性能测试 | 100% 关键路径 | 每日 | criterion |
| 兼容性测试 | 100% API端点 | 每次发布 | 自定义工具 |
| 端到端测试 | 100% 用户场景 | 每次发布 | 自定义工具 |

## 单元测试计划

### 1. 数据模型测试 (src/models/)

**测试文件**: `src/models/tests.rs`

**测试用例**:
- [ ] ModelProvider序列化/反序列化
- [ ] 字段验证规则 (name长度、URL格式等)
- [ ] 敏感字段过滤功能
- [ ] ModelConfig和ModelSettings验证
- [ ] 默认值设置正确性

**覆盖率目标**: >90%

### 2. Repository层测试 (src/repository/)

**测试文件**: `src/repository/tests.rs`

**测试用例**:
- [ ] CRUD操作正确性
- [ ] 查询构建器功能
- [ ] 数据库连接错误处理
- [ ] 事务处理
- [ ] 并发访问安全性
- [ ] 查询性能基准

**Mock策略**: 使用mockall创建Repository trait的mock实现

### 3. Service层测试 (src/services/)

**测试文件**: `src/services/tests.rs`

**测试用例**:
- [ ] 业务逻辑验证
- [ ] 内置提供商保护逻辑
- [ ] 名称唯一性检查
- [ ] 缓存机制功能
- [ ] 错误处理和传播
- [ ] 配置管理和热重载

**Mock依赖**: Repository、外部服务

### 4. Handler层测试 (src/handlers/)

**测试文件**: `src/handlers/tests.rs`

**测试用例**:
- [ ] HTTP请求解析
- [ ] 响应格式转换
- [ ] 错误响应格式
- [ ] 参数验证
- [ ] 中间件功能
- [ ] CORS处理

## 集成测试计划

### 1. API集成测试 (tests/integration/)

**测试文件**: `tests/integration/api_tests.rs`

**测试场景**:

#### 创建提供商测试
```rust
#[tokio::test]
async fn test_create_provider_success() {
    // 测试成功创建提供商
}

#[tokio::test]
async fn test_create_provider_duplicate_name() {
    // 测试重复名称错误
}

#[tokio::test]
async fn test_create_provider_invalid_data() {
    // 测试无效数据验证
}
```

#### 获取提供商测试
```rust
#[tokio::test]
async fn test_get_provider_success() {
    // 测试成功获取提供商
}

#[tokio::test]
async fn test_get_provider_not_found() {
    // 测试提供商不存在
}

#[tokio::test]
async fn test_get_provider_sensitive_fields_filtered() {
    // 测试敏感字段过滤
}
```

#### 更新提供商测试
```rust
#[tokio::test]
async fn test_update_provider_success() {
    // 测试成功更新
}

#[tokio::test]
async fn test_update_builtin_provider_protection() {
    // 测试内置提供商保护
}
```

#### 删除提供商测试
```rust
#[tokio::test]
async fn test_delete_provider_success() {
    // 测试成功删除
}

#[tokio::test]
async fn test_delete_builtin_provider_forbidden() {
    // 测试内置提供商删除保护
}
```

#### 搜索提供商测试
```rust
#[tokio::test]
async fn test_search_providers_basic() {
    // 测试基础搜索
}

#[tokio::test]
async fn test_search_providers_with_filters() {
    // 测试过滤搜索
}

#[tokio::test]
async fn test_search_providers_pagination() {
    // 测试分页功能
}
```

### 2. 数据库集成测试 (tests/integration/)

**测试文件**: `tests/integration/database_tests.rs`

**测试场景**:
- [ ] 数据库连接和断开
- [ ] 表结构创建和验证
- [ ] 数据一致性检查
- [ ] 并发操作安全性
- [ ] 事务回滚测试

### 3. 初始化系统测试 (tests/integration/)

**测试文件**: `tests/integration/initialization_tests.rs`

**测试场景**:
- [ ] 配置文件加载测试
- [ ] 内置提供商导入测试
- [ ] 重复导入防护测试
- [ ] 配置热重载测试
- [ ] 初始化错误恢复测试

## 性能测试计划

### 1. API性能测试 (benches/)

**测试文件**: `benches/api_benchmark.rs`

**性能目标**:
- 创建操作: P95 < 500ms
- 获取操作: P95 < 200ms
- 更新操作: P95 < 300ms
- 删除操作: P95 < 200ms
- 搜索操作: P95 < 500ms

**测试场景**:
```rust
use criterion::{black_box, criterion_group, criterion_main, Criterion};

fn benchmark_create_provider(c: &mut Criterion) {
    c.bench_function("create_provider", |b| {
        b.iter(|| {
            // 创建提供商性能测试
        })
    });
}

fn benchmark_search_providers(c: &mut Criterion) {
    c.bench_function("search_providers", |b| {
        b.iter(|| {
            // 搜索提供商性能测试
        })
    });
}
```

### 2. 并发测试 (benches/)

**测试文件**: `benches/concurrency_benchmark.rs`

**测试场景**:
- [ ] 100并发读取测试
- [ ] 50并发写入测试
- [ ] 混合读写并发测试
- [ ] 缓存并发访问测试

### 3. 内存和资源测试

**测试目标**:
- 内存使用 < 512MB
- 无内存泄漏
- 数据库连接池效率
- 缓存内存管理

## 兼容性测试计划

### 1. API兼容性测试 (tests/compatibility/)

**测试文件**: `tests/compatibility/go_api_compatibility.rs`

**测试策略**:
1. 录制Go版本API的请求/响应
2. 对Rust版本发送相同请求
3. 比较响应格式和内容
4. 验证错误码和错误消息

**测试用例**:
```rust
#[tokio::test]
async fn test_create_response_format_compatibility() {
    // 验证创建响应格式与Go版本一致
}

#[tokio::test]
async fn test_search_response_format_compatibility() {
    // 验证搜索响应格式与Go版本一致
}

#[tokio::test]
async fn test_error_response_format_compatibility() {
    // 验证错误响应格式与Go版本一致
}
```

### 2. 数据格式兼容性测试

**测试场景**:
- [ ] JSON序列化格式一致性
- [ ] 时间戳格式一致性
- [ ] 字段命名一致性
- [ ] 默认值处理一致性

## 端到端测试计划

### 1. 用户场景测试 (tests/e2e/)

**测试文件**: `tests/e2e/user_scenarios.rs`

**场景1: 新用户配置提供商**
```rust
#[tokio::test]
async fn test_new_user_setup_provider() {
    // 1. 系统启动，导入内置提供商
    // 2. 用户创建自定义提供商
    // 3. 用户配置API密钥
    // 4. 用户启用提供商
    // 5. 验证提供商可用
}
```

**场景2: 管理员管理提供商**
```rust
#[tokio::test]
async fn test_admin_manage_providers() {
    // 1. 查看所有提供商
    // 2. 搜索特定提供商
    // 3. 更新提供商配置
    // 4. 禁用/启用提供商
    // 5. 删除自定义提供商
}
```

**场景3: 系统升级和配置刷新**
```rust
#[tokio::test]
async fn test_system_upgrade_config_refresh() {
    // 1. 系统运行中
    // 2. 更新配置文件
    // 3. 触发配置重载
    // 4. 验证新配置生效
    // 5. 验证用户数据保持
}
```

### 2. 错误场景测试

**测试场景**:
- [ ] 数据库连接失败恢复
- [ ] 配置文件损坏处理
- [ ] 网络超时处理
- [ ] 并发冲突处理

## 测试数据管理

### 1. 测试数据准备

**内置提供商测试数据**:
```rust
pub fn create_test_builtin_provider() -> ModelProvider {
    ModelProvider {
        id: "test-builtin".to_string(),
        name: "Test Builtin Provider".to_string(),
        builtin: true,
        enabled: true,
        // ... 其他字段
    }
}
```

**自定义提供商测试数据**:
```rust
pub fn create_test_custom_provider() -> ModelProvider {
    ModelProvider {
        id: "test-custom".to_string(),
        name: "Test Custom Provider".to_string(),
        builtin: false,
        enabled: true,
        // ... 其他字段
    }
}
```

### 2. 测试环境隔离

**数据库隔离策略**:
- 每个测试使用独立的数据库命名空间
- 测试前清理数据
- 测试后自动清理

**配置隔离策略**:
- 使用临时配置文件
- 环境变量覆盖
- 测试专用配置

## 测试执行策略

### 1. 本地开发测试

```bash
# 运行所有单元测试
cargo test

# 运行集成测试
cargo test --test integration

# 运行性能测试
cargo bench

# 生成测试覆盖率报告
cargo tarpaulin --out Html
```

### 2. CI/CD管道测试

**阶段1: 快速反馈**
- 单元测试
- 代码格式检查
- 静态分析

**阶段2: 集成验证**
- 集成测试
- 兼容性测试
- 基础性能测试

**阶段3: 发布验证**
- 端到端测试
- 完整性能测试
- 安全扫描

### 3. 测试报告

**覆盖率报告**:
- 代码覆盖率 >80%
- 分支覆盖率 >75%
- 函数覆盖率 >90%

**性能报告**:
- 响应时间趋势
- 吞吐量对比
- 资源使用情况

**兼容性报告**:
- API兼容性检查结果
- 数据格式验证结果
- 错误处理一致性结果

## 测试工具和框架

### 1. 测试框架
- **tokio-test**: 异步测试支持
- **criterion**: 性能基准测试
- **mockall**: Mock对象生成
- **tarpaulin**: 代码覆盖率

### 2. 测试工具
- **reqwest**: HTTP客户端测试
- **serde_json**: JSON数据处理
- **tempfile**: 临时文件管理
- **uuid**: 测试数据生成

### 3. 断言库
```rust
use assert_matches::assert_matches;
use pretty_assertions::assert_eq;

// 使用示例
assert_eq!(response.status(), 200);
assert_matches!(result, Ok(provider) if provider.name == "test");
```

## 测试维护策略

### 1. 测试代码质量
- 测试代码也需要代码审查
- 保持测试的可读性和可维护性
- 避免测试间的依赖关系

### 2. 测试数据维护
- 定期更新测试数据
- 保持测试数据的真实性
- 清理过时的测试用例

### 3. 性能基准维护
- 定期更新性能基准
- 监控性能回归
- 优化慢速测试
