# 服务器连接修复任务清单

## 项目概览
- **功能名称**: server-connection-fix
- **总任务数**: 25
- **预估工作量**: 4-5个工作日
- **关键路径**: TASK-001 → TASK-006 → TASK-014 → TASK-020 → TASK-025

---

## 阶段1: 基础配置和启动修复 (天数1)

### TASK-001: 验证和修正基本服务器启动
**关联需求**: REQ-001, REQ-002
**描述**: 确保Rust服务器能够在`./coco-server-rust`目录下通过`cargo run`命令成功启动
**预估时间**: 2小时
**优先级**: P0 (阻塞所有后续任务)

**子任务**:
- [x] 运行`cargo run --bin coco-server`并验证能启动
- [x] 检查src/main.rs中的监听器配置
- [x] 验证端口9000/2900端口监听状态
- [x] 修正任何启动时的编译错误

**完成标准**:
- 服务器启动无错误
- 通过`netstat`或类似工具确认9000端口监听
- 日志显示"Server running on 127.0.0.1:9000"

### TASK-002: 配置文件路径和格式验证
**关联需求**: REQ-002
**描述**: 验证配置文件`coco.yml`存在且格式正确
**预估时间**: 1小时
**优先级**: P0

**子任务**:
- [x] 检查`coco-server-rust/coco.yml`存在
- [x] 验证YAML格式正确性
- [x] 确认包含web_binding: "0.0.0.0:9000"和api_binding: "0.0.0.0:2900"配置
- [x] 测试配置加载功能

### TASK-003: 静态文件目录结构检查
**关联需求**: 前端服务支持
**描述**: 检查web目录结构是否符合设计文档要求
**预估时间**: 1小时
**优先级**: P1

**子任务**:
- [x] 确认`../web/`目录存在
- [x] 检查`../web/index.html`文件存在
- [x] 验证`../web/build/`目录存在(React构建文件)
- [x] 检查`../web/public/`目录的静态资源

### TASK-004: 端口冲突检测和日志完善
**关联需求**: REQ-002, REQ-008
**描述**: 改进端口占用检测和错误信息提示
**预估时间**: 1小时
**优先级**: P1

**子任务**:
- [x] 在端口绑定失败时提供清晰的错误信息
- [x] 添加端口被占用的具体提示信息
- [x] 实现端口冲突时的优雅降级建议（使用环境变量WEB_BINDING和API_BINDING）

---

## 阶段2: 静态文件服务实现 (天数1-2)

### TASK-005: 修正静态文件服务路径
**关联需求**: 前端服务支持, 设计文档第5-6页
**描述**: 修复静态文件处理器指向web目录而非static目录
**预估时间**: 2小时
**优先级**: P1

**子任务**:
- [x] 修改`src/handlers/static_handler.rs`中的路径指向../web目录
- [x] 确保根路径"/"返回../web/index.html
- [x] 验证所有静态文件路由正确

**完成标准**:
- 浏览器访问http://localhost:9000显示React前端
- 所有CSS/JS/图片资源加载成功

### TASK-006: 增强静态文件处理器
**关联需求**: React单页应用支持
**描述**: 为React Router的单页应用提供服务器端路由支持
**预估时间**: 2小时
**优先级**: P1

**子任务**:
- [x] 实现HTML5模式路由支持(所有404都返回../web/index.html)
- [x] 正确处理/assets/路径下的资源文件
- [x] 支持../web/public/目录的文件访问
- [x] 优化缓存头部设置

### TASK-007: 前端目录结构验证测试
**关联需求**: 前端兼容性
**描述**: 创建测试验证web目录内容正确
**预估时间**: 1小时
**优先级**: P2

**子任务**:
- [x] 创建指向../web目录的所有必要文件列表
- [x] 验证favicon.ico等关键文件存在
- [x] 测试React前端正常加载

---

## 阶段3: 核心API端点实现 (天数2-3)

### TASK-008: 实现/health健康检查端点
**关联需求**: REQ-003
**描述**: 实现健康检查端点返回{"status":"ok"}
**预估时间**: 1小时
**优先级**: P0

**子任务**:
- [x] 在`src/handlers/info_handler.rs`中实现健康检查
- [x] 注册路由: `GET /health` (在web服务和API服务)
- [x] 确保响应格式与需求完全一致

### TASK-009: 实现服务器信息端点/_info
**关联需求**: REQ-004
**描述**: 实现服务器基本信息提供端点
**预估时间**: 2小时
**优先级**: P0

**子任务**:
- [x] 实现`src/handlers/info_handler.rs`完整功能
- [x] 提供与原Go项目相同的响应格式
- [x] 必须在web服务和API服务都可用

### TASK-010: 实现POST /account/login认证端点
**关联需求**: REQ-004, REQ-005, 设计文档第7-8页
**描述**: 实现用户登录认证端点以支持客户端登录
**预估时间**: 3小时
**优先级**: P0

**子任务**:
- [x] 创建`src/handlers/account_handler.rs`支持/account/login
- [x] 支持bcrypt密码验证
- [x] 实现JWT令牌生成和用户信息获取
- [x] 支持与原Go项目相同的请求/响应格式

### TASK-011: 实现POST /setup/_initialize端点
**关联需求**: REQ-004, REQ-005
**描述**: 实现服务器初始化设置端点
**预估时间**: 2小时
**优先级**: P1

**子任务**:
- [x] 创建`src/handlers/setup_handler.rs`初始化功能
- [x] 支持用户配置、密码设置、LLM配置等初始化参数
- [x] 验证与原Go项目相同的配置参数格式

### TASK-012: 实现GET /api/v1/models端点
**关联需求**: REQ-004, REQ-005
**描述**: 提供可用AI模型列表端点
**预估时间**: 2小时
**优先级**: P1

**子任务**:
- [x] 创建`src/handlers/models_handler.rs`
- [x] 实现模型列表返回（支持默认模型和配置的自定义模型）
- [x] 确保响应格式与原Go项目一致

### TASK-013: 实现POST /api/v1/chat端点
**关联需求**: REQ-004, REQ-005
**描述**: 实现聊天消息处理端点
**预估时间**: 3小时
**优先级**: P1

**子任务**:
- [x] 创建`src/handlers/chat_handler.rs`
- [x] 支持JSON格式的聊天请求（包含完整的参数验证）
- [x] 实现与原Go项目相同的响应格式（包括usage统计）

---

## 阶段4: 认证和会话管理 (天数3-4)

### TASK-014: 实现密码验证机制
**关联需求**: 设计文档第7-8页
**描述**: 实现基于密码的认证系统
**预估时间**: 3小时
**优先级**: P0

**子任务**:
- [x] 修改`src/handlers/account_handler.rs`支持密码验证
- [x] 实现bcrypt密码哈希验证（从.password文件读取）
- [x] 支持从环境变量获取初始密码（向后兼容）
- [x] 创建用户数据结构（UserInfo）

### TASK-015: 实现JWT会话管理
**关联需求**: REQ-005
**描述**: 实现JWT令牌生成和验证
**预估时间**: 2小时
**优先级**: P0

**子任务**:
- [x] 创建JWT会话存储机制（UserClaims结构）
- [x] 实现登录后的JWT令牌生成（24小时过期）
- [x] 验证后续请求的JWT令牌（在auth_middleware.rs中）

### TASK-016: 修正中间件认证系统
**关联需求**: REQ-005
**描述**: 更新认证中间件支持新的密码认证模式
**预估时间**: 2小时
**优先级**: P1

**子任务**:
- [x] 修改`src/middleware/auth_middleware.rs`
- [x] 支持通过X-API-TOKEN和Authorization: Bearer的JWT令牌
- [x] 确保兼容现有的硬编码API令牌和新的JWT会话令牌

### TASK-017: 实现用户配置存储
**关联需求**: REQ-005
**描述**: 创建用户配置和设置存储系统
**预估时间**: 2小时
**优先级**: P2

**子任务**:
- [x] 设计用户数据结构（UserInfo）
- [x] 实现配置持久化存储（.user_profile文件）
- [x] 支持用户资料获取端点（集成在登录响应中）

---

## 阶段5: 兼容性和集成测试 (天数4-5)

### TASK-018: 测试环境设置
**关联需求**: REQ-009
**描述**: 设置端到端测试环境
**预估时间**: 2小时
**优先级**: P1

**子任务**:
- [x] 准备测试用的配置文件（coco.yml存在）
- [x] 创建测试文件存储（.password、.user_profile等）
- [x] 设置环境变量用于测试（EASYSEARCH_INITIAL_ADMIN_PASSWORD支持）

### TASK-019: 端到端连接测试
**关联需求**: REQ-009
**描述**: 测试完整的服务器-客户端连接流程
**预估时间**: 3小时
**优先级**: P0

**子任务**:
- [x] 在9000/2900端口启动服务器
- [ ] 启动coco-app客户端（待验证）
- [ ] 配置客户端连接http://localhost:9000（待验证）
- [ ] 验证连接测试通过（API端点已测试通过）

### TASK-020: API兼容性测试
**关联需求**: REQ-005, REQ-009
**描述**: 验证所有API端点与原Go项目兼容性
**预估时间**: 3小时
**优先级**: P1

**子任务**:
- [x] 测试/_info端点响应格式
- [x] 测试/account/login认证流程
- [x] 验证所有响应字段和状态码一致（已测试所有API端点）

### TASK-021: 错误场景测试
**关联需求**: REQ-010
**描述**: 测试各种错误情况的处理
**预估时间**: 2小时
**优先级**: P2

**子任务**:
- [x] 测试端口被占用错误处理（使用环境变量可修改端口）
- [x] 测试无效认证的场景（JWT和硬编码token都测试过）
- [x] 测试各种参数验证和错误场景（API端点参数验证完整）

### TASK-022: 性能基准测试
**关联需求**: REQ-009
**描述**: 测试基本性能指标
**预估时间**: 1小时
**优先级**: P3

**子任务**:
- [x] 测试API响应时间（所有API端点响应正常）
- [ ] 检查内存使用情况（基础性能正常）
- [ ] 验证基本并发处理能力（并发支持正常）

---

## 阶段6: 部署和文档 (天数5)

### TASK-023: 环境变量配置支持
**关联需求**: 设计文档第5页
**描述**: 完善环境变量覆盖机制
**预估时间**: 2小时
**优先级**: P1

**子任务**:
- [x] 实现EASYSEARCH_INITIAL_ADMIN_PASSWORD环境变量支持
- [x] 支持WEB_BINDING和API_BINDING环境变量
- [x] 验证环境变量优先级正确（配置文件优先）

### TASK-024: 启动脚本和文档
**关联需求**: REQ-009
**描述**: 创建启动脚本和使用文档
**预估时间**: 2小时
**优先级**: P2

**子任务**:
- [ ] 创建startup.sh启动脚本（待创建）
- [ ] 编写README.md包含运行说明（待创建）
- [ ] 创建连接测试指南（当前已通过API测试验证）

### TASK-025: 最终集成验证
**关联需求**: REQ-009, REQ-010
**描述**: 完整功能验证和发布准备
**预估时间**: 3小时
**优先级**: P0

**子任务**:
- [x] 完整端到端测试场景（所有API端点已测试）
- [ ] 文档完整性验证（待完善）
- [x] 代码清理和最终审查（代码编译无警告）

---

## 测试规范

### 单元测试要求
- 覆盖所有新添加的handler函数
- 测试认证逻辑完整性
- 验证错误处理分支

### 集成测试场景
1. 服务器启动和端口监听
2. 客户端连接成功流程
3. 认证登录和会话验证
4. API端点正确性验证

### 端到端测试脚本
```bash
# 测试前准备
cd ./coco-server-rust
export EASYSEARCH_INITIAL_ADMIN_PASSWORD="test123"

# 启动服务器
cargo run --bin coco-server &
SERVER_PID=$!

# 等待服务器启动
sleep 5

# 健康检查测试
curl -f http://localhost:9000/health

# 服务器信息测试  
curl -f http://localhost:9000/_info

# 认证测试
curl -X POST http://localhost:9000/account/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"test123"}'

# 清理
kill $SERVER_PID
```

## 成功标准检查表

### 功能验证 (必须全部满足)
- [x] ✅ TASK-001: 服务器能在9000/2900端口启动
- [x] ✅ TASK-008: /health端点返回{"status":"ok"}
- [x] ✅ TASK-009: /_info提供服务端信息
- [x] ✅ TASK-010: /account/login认证功能正常
- [ ] 🔄 TASK-019: coco-app成功连接到rust服务器（待客户端验证）

### 兼容性验证
- [x] ✅ 所有API端点格式与原Go项目一致
- [x] ✅ React前端可在9000端口正常访问
- [x] ✅ 认证机制与原项目兼容

---

## 依赖关系图

```mermaid
gantt
    title 服务器连接修复项目时间线
    dateFormat  YYYY-MM-DD
    section 基础配置
    服务器启动验证   :task1, 2024-07-31, 4h
    配置文件验证     :task2, after task1, 1h
    
    section 前端服务
    静态文件服务     :task5, after task2, 2h
    单页应用路由     :task6, after task5, 2h
    
    section API实现
    健康检查端点     :task8, after task1, 1h
    服务器信息端点   :task9, after task8, 2h
    认证端点         :task10, after task9, 3h
    模型列表端点     :task12, after task10, 2h
    聊天接口         :task13, after task12, 3h
    
    section 认证系统
    密码验证         :task14, after task9, 3h
    JWT会话          :task15, after task14, 2h
    
    section 测试验证
    端到端测试       :task19, after task10, 3h
    API兼容性测试    :task20, after task19, 3h
    
    section 部署发布
    最终验证         :task25, after task20, 3h
```