# 服务器连接修复需求文档

## 功能概述

修复Rust重写的coco-server与coco-app客户端之间的连接问题，确保客户端能成功连接到运行在9000端口的Rust服务器，实现客户端与服务器的正常通信。这是Rust重写项目的关键连接点，直接影响客户端功能的可用性。

根据对原Go项目的配置文件分析，发现原项目使用2900端口作为API端口，9000端口作为Web端口。为了保持一致性并避免端口冲突，Rust重写项目应使用9000端口与客户端通信。

## 用户故事

1. **作为**开发人员，**我想要**Rust服务器正确启动并监听9000端口，**以便**客户端能够建立初始连接。
2. **作为**coco-app用户，**我想要**客户端能成功连接到Rust重写的服务器，**以便**我能正常使用AI功能。
3. **作为**系统架构师，**我想要**确保Rust服务器提供与原Go项目相同的API端点，**以便**保持客户端兼容性。
4. **作为**测试工程师，**我想要**有明确的健康检查端点，**以便**能验证服务器是否正常运行。

## 验收标准

### 基本连接要求
1. **REQ-001**：Rust服务器必须能够在`./coco-server-rust`目录下通过`cargo run`命令成功启动。
2. **REQ-002**：服务器必须监听`127.0.0.1:9000`地址和端口。
3. **REQ-003**：服务器必须提供`/health`端点，返回`{"status":"ok"}`作为响应，用于连接验证。

### API兼容性要求
4. **REQ-004**：服务器必须实现原Go项目中的核心API端点，包括但不限于：
   - `POST /api/v1/auth/login`
   - `GET /api/v1/models`
   - `POST /api/v1/chat`
5. **REQ-005**：所有API端点必须使用与原Go项目相同的请求和响应格式。

### 客户端集成要求
6. **REQ-006**：coco-app必须能够通过用户界面配置Rust服务器地址为`http://127.0.0.1:9000`。
7. **REQ-007**：连接测试功能必须验证服务器端点是否存在且可访问。
8. **REQ-008**：错误信息必须清晰指示连接问题（如端口错误、服务器未运行等），而非仅显示"端点未找到"。

### 测试要求
9. **REQ-009**：必须提供端到端测试验证：
   - 启动Rust服务器
   - 启动coco-app
   - 配置并验证连接成功
10. **REQ-010**：必须包含针对连接失败场景的单元测试（如端口被占用、服务器未启动等）。

## 技术约束
- 保持与原Go项目相同的API行为和数据格式
- 服务器必须在本地开发环境中运行，无需额外配置即可与coco-app连接
- 不得修改coco-app的客户端代码，仅修改Rust服务器实现
- 使用9000端口而非2900端口，以符合原项目的Web端口配置

## 成功标准
- coco-app能够成功连接到Rust服务器并进行基本功能测试
- 无"端点未找到"连接错误
- API调用返回预期响应